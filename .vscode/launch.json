{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Chrome - Frontend (5173)",
            "url": "http://localhost:5173",
            "webRoot": "${workspaceFolder}",
            "sourceMaps": true,
            "userDataDir": false,
            "runtimeArgs": [
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
        },
        {
            "type": "chrome",
            "request": "attach",
            "name": "Attach to Chrome",
            "port": 9222,
            "webRoot": "${workspaceFolder}",
            "sourceMaps": true
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Node.js Program",
            "program": "${workspaceFolder}/index.js",
            "console": "integratedTerminal",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "attach",
            "name": "Attach to Node.js",
            "port": 9229,
            "restart": true,
            "localRoot": "${workspaceFolder}",
            "remoteRoot": ".",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Launch Web App (Yarn)",
            "runtimeExecutable": "yarn",
            "runtimeArgs": [
                "start:web"
            ],
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Jest Tests",
            "program": "${workspaceFolder}/node_modules/.bin/jest",
            "args": [
                "--runInBand",
                "--no-cache",
                "--no-coverage"
            ],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Electron Main",
            "program": "${workspaceFolder}/main.js",
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron",
            "runtimeArgs": [
                "--inspect=5858",
                "."
            ],
            "env": {
                "NODE_ENV": "development"
            }
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Electron Renderer",
            "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron",
            "runtimeArgs": [
                "${workspaceFolder}/main.js",
                "--remote-debugging-port=9223"
            ],
            "webRoot": "${workspaceFolder}",
            "timeout": 30000
        }
    ],
    "compounds": [
        {
            "name": "Launch Electron",
            "configurations": [
                "Electron Main",
                "Electron Renderer"
            ]
        },
        {
            "name": "Launch Full Stack",
            "configurations": [
                "Launch Web App (Yarn)",
                "Launch Chrome - Frontend (5173)"
            ]
        }
    ]
}