steps:
  # Step 1: Use a Node.js 20 builder image
  - name: 'node:20'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Display Node.js version to confirm
        node -v
        npm -v
        
  # Step 2: Install all packages
  - name: 'node:20'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Remove conflicting package-lock.json if it exists
        rm -f package-lock.json
        # Install dependencies
        yarn install --ignore-engines

  # Step 3: Create .env file from substitutions
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cat > .env << EOF
        VITE_SUPABASE_URL=${_SUPABASE_URL}
        VITE_SUPABASE_ANON_KEY=${_SUPABASE_ANON_KEY}
        VITE_API_BASE_URL=${_API_BASE_URL}
        VITE_GITHUB_APP_URL=${_GITHUB_APP_URL}
        VITE_GITHUB_CLIENT_ID=${_GITHUB_CLIENT_ID}
        VITE_API_BASE_URL_APP_PUBLIC_POSTHOG_KEY=${_APP_PUBLIC_POSTHOG_KEY}
        VITE_APP_PUBLIC_POSTHOG_HOST=${_APP_PUBLIC_POSTHOG_HOST}
        EOF
        
  # Step 4: Build the web application
  - name: 'node:20'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        yarn build:web
    env:
      - 'VITE_SUPABASE_URL=${_SUPABASE_URL}'
      - 'VITE_SUPABASE_ANON_KEY=${_SUPABASE_ANON_KEY}'
      - 'VITE_API_BASE_URL=${_API_BASE_URL}'
      - 'VITE_GITHUB_APP_URL=${_GITHUB_APP_URL}'
      - 'VITE_GITHUB_CLIENT_ID=${_GITHUB_CLIENT_ID}'
      - 'VITE_API_BASE_URL_APP_PUBLIC_POSTHOG_KEY=${_APP_PUBLIC_POSTHOG_KEY}'
      - 'VITE_APP_PUBLIC_POSTHOG_HOST=${_APP_PUBLIC_POSTHOG_HOST}'

  # Step 5: Create app.yaml in the dist folder with HTTPS redirect
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cat > dist/app.yaml << 'EOL'
        runtime: nodejs20
        env: standard
        service: ${_ENV_NAME}
        handlers:
          - url: /(.*\..+)$
            static_files: \1
            upload: (.*\..+)$
            secure: always
            redirect_http_response_code: 301
          - url: /.*
            static_files: index.html
            upload: index.html
            secure: always
            redirect_http_response_code: 301
        EOL
  # Step 5.5: Check if App Engine exists and create if it doesn't
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Check if App Engine exists
        if ! gcloud app describe > /dev/null 2>&1; then
          echo "Creating App Engine application in region us-central..."
          gcloud app create --region=us-central
        else
          echo "App Engine application already exists."
        fi

  # Step 6: Deploy to App Engine
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'app'
      - 'deploy'
      - 'dist/app.yaml'
      - '--service-account=<EMAIL>'
      - '--quiet'

# Specify timeout (optional)
timeout: '1800s'  # 30 minutes

# Options
options:
  logging: CLOUD_LOGGING_ONLY

# Define substitutions for environment variables
substitutions:
  _SUPABASE_URL: 'https://oakrnplafjvuupqxdokt.supabase.co'
  _SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.J3S0Yl39wX2LLfxDTpx4wyRThBW6zC-7Aq5Zs5GFOyM'
  _API_BASE_URL: 'https://agent-service.emergentagent.com'
  _GITHUB_APP_URL: 'https://github.com/apps/emergent-staging/installations/new'
  _GITHUB_CLIENT_ID: 'Iv23liJD1SI1Rh8FHtUH'
  _APP_PUBLIC_POSTHOG_KEY: 'phc_byuJdfe3Bw2cQNF9KiLnPgA7XiRGVTxcoFMV0gKGTXU'
  _APP_PUBLIC_POSTHOG_HOST: 'https://us.i.posthog.com'
  _ENV_NAME: 'default'
