<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_10122_36256" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
<rect width="24" height="24" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_10122_36256)">
<g filter="url(#filter0_d_10122_36256)">
<path d="M11 22V17C11 16.0667 10.8583 15.375 10.575 14.925C10.2917 14.475 9.91667 14.0333 9.45 13.6L10.875 12.175C11.075 12.3583 11.2667 12.5542 11.45 12.7625C11.6333 12.9708 11.8167 13.1917 12 13.425C12.2333 13.1083 12.4708 12.8292 12.7125 12.5875C12.9542 12.3458 13.2 12.1083 13.45 11.875C14.0833 11.2917 14.6583 10.6167 15.175 9.85C15.6917 9.08333 15.9667 7.74167 16 5.825L14.425 7.4L13 6L17 2L21 6L19.6 7.4L18 5.825C17.9667 8.20833 17.6 9.90417 16.9 10.9125C16.2 11.9208 15.5 12.7417 14.8 13.375C14.2667 13.8583 13.8333 14.3292 13.5 14.7875C13.1667 15.2458 13 15.9833 13 17V22H11ZM6.2 8.175C6.13333 7.84167 6.0875 7.475 6.0625 7.075C6.0375 6.675 6.01667 6.25833 6 5.825L4.4 7.4L3 6L7 2L11 6L9.575 7.4L8 5.85C8 6.2 8.01667 6.52917 8.05 6.8375C8.08333 7.14583 8.11667 7.43333 8.15 7.7L6.2 8.175ZM8.35 12.575C8.01667 12.225 7.69583 11.8167 7.3875 11.35C7.07917 10.8833 6.80833 10.3083 6.575 9.625L8.5 9.15C8.66667 9.6 8.85833 9.98333 9.075 10.3C9.29167 10.6167 9.525 10.9 9.775 11.15L8.35 12.575Z" fill="#80FFF9"/>
</g>
</g>
<defs>
<filter id="filter0_d_10122_36256" x="-17" y="-18" width="58" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.501961 0 0 0 0 1 0 0 0 0 0.976471 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10122_36256"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10122_36256" result="shape"/>
</filter>
</defs>
</svg>
