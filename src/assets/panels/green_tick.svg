<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_10295_78486)">
<path d="M27 36C25.755 36 24.585 35.7638 23.49 35.2913C22.395 34.8188 21.4425 34.1775 20.6325 33.3675C19.8225 32.5575 19.1813 31.605 18.7088 30.51C18.2362 29.415 18 28.245 18 27C18 25.755 18.2362 24.585 18.7088 23.49C19.1813 22.395 19.8225 21.4425 20.6325 20.6325C21.4425 19.8225 22.395 19.1813 23.49 18.7088C24.585 18.2362 25.755 18 27 18C28.245 18 29.415 18.2362 30.51 18.7088C31.605 19.1813 32.5575 19.8225 33.3675 20.6325C34.1775 21.4425 34.8188 22.395 35.2913 23.49C35.7638 24.585 36 25.755 36 27C36 28.245 35.7638 29.415 35.2913 30.51C34.8188 31.605 34.1775 32.5575 33.3675 33.3675C32.5575 34.1775 31.605 34.8188 30.51 35.2913C29.415 35.7638 28.245 36 27 36Z" fill="#5FE55C"/>
</g>
<g filter="url(#filter1_d_10295_78486)">
<path d="M30.7155 23C30.8953 23 31.0623 23.0314 31.2164 23.0941C31.3706 23.1568 31.5076 23.2467 31.6275 23.3637C31.7474 23.4808 31.8373 23.6125 31.8972 23.7588C31.9572 23.9051 31.9914 24.0702 32 24.2542C32 24.4214 31.9679 24.5824 31.9037 24.737C31.8394 24.8917 31.7474 25.0276 31.6275 25.1446L26.5279 30.13C26.408 30.2471 26.2689 30.337 26.1105 30.3997C25.952 30.4624 25.7872 30.4958 25.6159 30.5C25.4447 30.5 25.2798 30.4686 25.1214 30.4059C24.963 30.3432 24.8238 30.2513 24.7039 30.13L22.3789 27.8599C22.259 27.7429 22.167 27.607 22.1028 27.4523C22.0385 27.2977 22.0043 27.1367 22 26.9695C22 26.7939 22.0343 26.6309 22.1028 26.4804C22.1713 26.3298 22.2633 26.1961 22.3789 26.079C22.4945 25.962 22.6294 25.8742 22.7836 25.8156C22.9377 25.7571 23.1068 25.7237 23.2909 25.7153C23.4622 25.7153 23.6271 25.7467 23.7855 25.8094C23.9439 25.8721 24.0831 25.962 24.203 26.079L25.6159 27.4586L29.8035 23.3637C29.9234 23.2467 30.0625 23.1568 30.2209 23.0941C30.3794 23.0314 30.5442 23 30.7155 23Z" fill="#172218"/>
</g>
<defs>
<filter id="filter0_d_10295_78486" x="0" y="0" width="54" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="9"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.372549 0 0 0 0 0.898039 0 0 0 0 0.360784 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10295_78486"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10295_78486" result="shape"/>
</filter>
<filter id="filter1_d_10295_78486" x="2" y="3" width="50" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.372549 0 0 0 0 0.898039 0 0 0 0 0.360784 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10295_78486"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10295_78486" result="shape"/>
</filter>
</defs>
</svg>
