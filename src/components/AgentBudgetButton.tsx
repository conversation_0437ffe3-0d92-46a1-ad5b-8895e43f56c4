import { cn } from "@/lib/utils";
import { Button } from "./ui/button";
import { useState } from "react";

interface AgentBudgetButtonProps {
    title?: string;
    onClick: () => void;
    className?: string;
    children?: React.ReactNode;
    variant?: "filled" | "outlined";
    icon?: string;
    hoverIcon?: string;
    disabled?: boolean;
    style?: React.CSSProperties;
}

function AgentBudgetButton({
    title,
    onClick,
    style,
    className = "",
    children,
    variant = "filled",
    icon,
    hoverIcon,
    disabled = false,
}: AgentBudgetButtonProps) {
    const [isHovering, setIsHovering] = useState(false);

    const buttonStyles = variant === "filled"
        ? "bg-[#F3CA5F] text-[#0F0F10]"
        : "border border-blue-500 bg-[#E6E6E6] border-[1px] border-[#333333] text-[#0F0F10]";

    const handleMouseEnter = () => {
        setIsHovering(true);
    };

    const handleMouseLeave = () => {
        setIsHovering(false);
    };

    // Determine which icon to display based on hover state
    const displayIcon = isHovering && hoverIcon ? hoverIcon : icon;

    return (
        <Button
            style={style}
            className={cn(
                "flex items-center gap-[12px] max-h-[44px] rounded-full px-4 py-3  transition-none text-[13px] md:text-[16px]",
                buttonStyles,
                className
            )}
            onClick={onClick}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            disabled={disabled}
        >
            {children ? children :
                <div className={className}>
                    <span className="font-semibold">
                        {title}
                    </span>
                    {displayIcon && <img title="Icon" src={displayIcon} className="" alt="" />}

                </div>
            }
        </Button>
    );
}

export default AgentBudgetButton;