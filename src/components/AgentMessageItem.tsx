import { useState, memo, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import RobotIcon from "@/assets/bot.svg";
import { AgentMessage as AgentMessageType } from "@/types/message";
import { MessageActions } from "./MessageActions";
import ipc from "@/lib/ipc/client";
import { useToast } from "@/hooks/use-toast";
import type { NeonConfig } from "@/lib/ipc/client";
import { GitHubPushModal } from "@/components/modals/GitHubPushModal";
import { Message } from "ai/react/dist";
import { ImageGallery } from "./ImageGallery";
import { AgentFinishActions } from "./agent/AgentFinishActions";
import {
  formatMessageTimestamp,
} from "@/lib/utils/dateFormatter";
import AgentBudgetButton from "./AgentBudgetButton";
import GithubIcon from "@/assets/github_dark.svg";
import InfoSquaredRounded from "@/assets/info-squared-black.svg";

// Icons
import IncreaseBudget from "@/assets/increase_budget.svg";
import EyeSVG from "@/assets/eye.svg";
import { useAuth, useCredits } from "@/contexts";

import SubagentButton from "./SubagentButton";
import { StatusIndicators } from "./agent/StatusIndicators";
import { MessageContent } from "./agent/MessageContent";
import { ActionButton } from "./agent/ActionButton";
import { SpecialMessageHandlers } from "./agent/SpecialMessageHandlers";
import { ErrorDisplay } from "./agent/ErrorDisplay";
import { BottomStatusSeparators, TopStatusSeparators } from "./agent/StatusSeparators";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";

export interface AgentMessageItemProps {
  message: AgentMessageType;
  userInitials: string;
  onMergeToLocal?: () => void;
  onShowSubagentMessages?: (
    messages: AgentMessageType["subagent_trajectory"],
    stepNum: any
  ) => void;
  onSubagentClick?: (message: AgentMessage) => void;
  hideRobotIcon?: boolean;
  lastGithubUsed?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null;
  marginBottom?: boolean;
  hideSubagentCount?: boolean;
  handleOpenVsCode?: () => void;
  handleRollback?: () => void;
  isCloudFlow?: boolean;
  searchActive?: boolean;
  searchQuery?: string;
  searchHighlights?: Array<{ startIndex: number; endIndex: number }>;
  isMatchingMessage?: boolean;
  isSubagent?: boolean;
  showActions?: boolean;
  hideImportantActions?: boolean;
  selectedMessageId?: string;
  handleAddToken?: (variant: "increase_budget" | "add_credits") => void;
  acc_cost?: number;
  max_budget?: number;
  togglePanel: ({
    panelName,
    value,
  }: {
    panelName:
      | "showUrlPreviewPanel"
      | "showLogsPanel"
      | "showSubagentPanel"
      | "showInfoPanel";
    value?: boolean | null;
  }) => void;
  currentChunk: any;
  isLastMessage?: boolean;
  agentState?: any;
  nextMessageExists?: boolean;
  panelState?: any;
  jobDetails?: {
    job_id: string;
    traj_path?: string;
    container_id?: string;
    createdBy?: string;
  };
  agentName?: string;
  onPause?: () => void;
  isPauseLoading?: boolean;
  podIsPaused?: boolean;
  isSubagentActive?: boolean;
  modalOpen?: {
    fork: boolean;
  };
  setModalOpen?: (open: {
    fork: boolean;
  }) => void;
  forkStatus?: "running" | "success" | "failed" | null;
}

interface AgentMessage extends Message {
  action?: string;
  observation?: string;
  env_success?: boolean;
  containerId?: string;
  agent_name?: string;
  error?: boolean;
  error_message?: string;
  subagent_trajectory?: {
    id: string;
    role: "user" | "assistant";
    content: string;
    timestamp: string;
  }[];
}

export const AgentMessageItem = memo(
  ({
    message,
    onSubagentClick,
    hideRobotIcon,
    handleOpenVsCode,
    handleRollback,
    marginBottom = true,
    hideSubagentCount = false,
    isCloudFlow = false,
    searchActive = false,
    // searchQuery = "",
    searchHighlights = [],
    isMatchingMessage = false,
    isSubagent = false,
    hideImportantActions = false,
    handleAddToken,
    lastGithubUsed,
    acc_cost,
    max_budget,
    togglePanel,
    currentChunk,
    isLastMessage = false,
    agentState,
    nextMessageExists = false,
    panelState,
    selectedMessageId,
    jobDetails,
    agentName,
    onPause,
    isPauseLoading,
    podIsPaused = false,
    isSubagentActive = false,
    modalOpen,
    forkStatus,
    setModalOpen,
  }: AgentMessageItemProps) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isGitHubPushModalOpen, setIsGitHubPushModalOpen] = useState(false);
    const ref = useRef<HTMLDivElement>(null);

    //Hooks
    const { toast } = useToast();

    const { credits, refreshCredits, tier } = useCredits();
    const { user, session } = useAuth();

    const isEmergentUser = useIsEmergentUser();

    let userName;

    if (user?.user_metadata?.custom_name) {
      userName = user.user_metadata.custom_name;
    } else if (user?.user_metadata?.full_name) {
      userName = user.user_metadata.full_name.split(" ")[0];
    } else {
      userName = user?.email?.split("@")[0];
    }

    useEffect(() => {
      if (
        message.action === "exit_cost" ||
        message.action === "exit_cost_credit_limit_reached" ||
        message.action === "finish" ||
        message.action === "context_limit_reached" ||
        message.action === "pause"
      ) {
        refreshCredits();
      }
    }, [message.action]);

    useEffect(() => {
      if (isMatchingMessage && searchActive && !isExpanded) {
        setIsExpanded(true);
      }
    }, [isMatchingMessage, searchActive, isExpanded]);

    const handleCopyToHost = async () => {
      if (!message.containerId) return;
      try {
        setIsLoading(true);
        const result = await ipc.showDirectoryPicker();
        if (result && !result.canceled && result.filePaths[0]) {
          const targetDir = result.filePaths[0];
          const config: NeonConfig = {
            type: "copy-to-host-as-archive",
            hostDir: targetDir,
          };

          const actionResult = await ipc.executeNeonAction(
            message.containerId,
            config
          );
          if (actionResult.success) {
            toast({
              title: "Export as folder",
              description: actionResult.message,
            });
          } else {
            toast({
              title: "Error",
              description: actionResult.error || "Failed to copy files",
              variant: "destructive",
            });
          }
        }
      } catch (error) {
        console.error("Failed to copy to host:", error);
        toast({
          title: "Error",
          description: "Failed to copy files to host",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const handleDeploy = async () => {
      // open the link in browser
      ipc.openExternal(
        "https://half-knave-03b.notion.site/Emergent-How-to-deploy-your-app-1ad3b3a7b06180ccbbdef789aeeecb8a"
      );
    };

    const handlePushToGithub = async () => {
      if (!message.containerId) return;
      setIsGitHubPushModalOpen(true);
    };

    const handleSubagentClick = () => {
      if (message.subagent_trajectory?.length && onSubagentClick) {
        const isCurrentMessageSelected = selectedMessageId === message.id;

        if (isCurrentMessageSelected && panelState.showSubagentPanel) {
          togglePanel({ panelName: "showSubagentPanel", value: false });
        } else if (panelState.showSubagentPanel && !isCurrentMessageSelected) {
          togglePanel({ panelName: "showSubagentPanel", value: false });
          setTimeout(() => {
            onSubagentClick(message as any);
          }, 300);
        } else {
          onSubagentClick(message as any);
        }
      }
    };

    return (
      <div
        ref={ref}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "relative group selection:text-[#66EAFF] selection:bg-[#66EAFF] selection:bg-opacity-10",
          marginBottom && "mb-0 my-3 md:my-5 space-y-[10px]"
        )}
        id={message.id}
      >
        {message.base64_image_list && message.base64_image_list.length > 0 && (
          <ImageGallery
            images={message.base64_image_list}
            className="justify-end"
          />
        )}

        <TopStatusSeparators
          message={message as any}
          credits={credits}
          max_budget={max_budget}
          user={user}
          session={session}
        />

        <div className="overflow-hidden rounded-lg">
          <div className="flex flex-col w-full space-y-4">
            <div className="items-center w-full max-w-4xl mx-auto w-4xl">
              <div
                className={cn(
                  "grid gap-2  md:gap-3",
                  isSubagent
                    ? "grid-cols-1 row-span-1"
                    : " grid-cols-1 md:grid-cols-[40px_1fr] row-span-1"
                )}
              >
                {!hideRobotIcon && !isSubagent && (
                  <div
                    className={cn(
                      "hidden row-span-2 md:block",
                      message.function_name === "rollback" && "row-span-1"
                    )}
                  >
                    <Avatar className="flex-shrink-0 w-10 h-10">
                      <AvatarFallback className="bg-transparent">
                        <img
                          src={RobotIcon}
                          alt="Robot"
                          className="h-8 w-fit"
                        />
                      </AvatarFallback>
                    </Avatar>
                  </div>
                )}
                <div
                  className={cn(
                    "min-w-0 w-full",
                    hideRobotIcon && "col-span-2"
                  )}
                >
                  <div className="flex flex-col prose prose-invert max-w-none">
                    <MessageContent
                      message={message as any}
                      userName={userName}
                      searchActive={searchActive}
                      searchHighlights={searchHighlights}
                      handleRollback={handleRollback}
                    />
                  </div>
                </div>
                <div
                  className={cn(
                    "  w-full",
                    hideRobotIcon ? "col-span-2" : "col-span-1",
                    message.function_name !== "rollback"
                      ? "space-y-1  md:space-y-[10px]"
                      : "h-[0px]"
                  )}
                >
                  {message.action &&
                    message.action === "finish" &&
                    message.function_name != "rollback" &&
                    !isSubagent && (
                      <AgentFinishActions
                        containerId={message.containerId}
                        isCloudFlow={isCloudFlow}
                        hideImportantActions={hideImportantActions}
                        handleDeploy={handleDeploy}
                        handleCopyToHost={handleCopyToHost}
                        handleOpenVsCode={handleOpenVsCode as any}
                        isLoading={isLoading}
                        handlePushToGithub={handlePushToGithub}
                        togglePanel={togglePanel as any}
                        podIsPaused={podIsPaused}
                      />
                    )}

                  <ActionButton
                    message={message as any}
                    isExpanded={isExpanded}
                    setIsExpanded={setIsExpanded}
                    isSubagent={isSubagent}
                  />

                  <SpecialMessageHandlers
                    message={message as any}
                    nextMessageExists={nextMessageExists}
                  />

                  <StatusIndicators
                    message={message as any}
                    credits={credits}
                    handleAddToken={handleAddToken}
                    podIsPaused={podIsPaused}
                    modalOpen={modalOpen}
                    setModalOpen={setModalOpen}
                  />

                  <ErrorDisplay message={message} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Show Custom UI for context_limit_reached */}

        {user &&
        session &&
        message.action === "context_limit_reached" &&
        !isEmergentUser ? (
          <div className="max-w-4xl pt-[6px] mx-auto">
            <div className="relative flex items-center gap-1 md:gap-4 md:ml-14">
              <AgentBudgetButton
                title="Save Work"
                className="flex bg-[#2EE572] hover:bg-[#2ee550] justify-between gap-2 items-center"
                variant="outlined"
                icon={GithubIcon}
                onClick={handlePushToGithub}
                disabled={podIsPaused}
              />
              <AgentBudgetButton
                title="Learn How to Clear Memory"
                className="flex items-center justify-between gap-2 font-brockmann"
                variant="outlined"
                icon={InfoSquaredRounded}
                onClick={() => {
                  window.open(
                    "https://half-knave-03b.notion.site/Emergent-Tips-and-Tricks-1dc3b3a7b0618095921ac5850421bab5",
                    "_blank"
                  );
                }}
              />
            </div>
          </div>
        ) : null}

        {/* Show Custom UI for exit_cost & exit_cost_credit_limit_reached */}

        {user &&
        session &&
        !hideImportantActions &&
        (message.action === "exit_cost_credit_limit_reached" ||
          message.action == "exit_cost") ? (
          <div className="max-w-4xl pt-[6px] mx-auto">
            <div className="relative flex items-center gap-4 ml-14">
              <AgentBudgetButton
                disabled={podIsPaused}
                title={
                  message.action === "exit_cost"
                    ? "Increase Budget"
                    : tier === "free"
                    ? "Buy Credits"
                    : "Add More Credits"
                }
                variant="filled"
                style={
                  tier == "free"
                    ? {
                        background: `
      linear-gradient(180deg, #ffffff, #000000),
      #F3CA5F
    `,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        backgroundBlendMode: "overlay, normal",
                      }
                    : {}
                }
                className=" hover:bg-[#F3CA5F] flex items-center gap-1 md:gap-4"
                onClick={() => {
                  handleAddToken &&
                    handleAddToken(
                      message.action === "exit_cost_credit_limit_reached"
                        ? "add_credits"
                        : "increase_budget"
                    );
                }}
                icon={IncreaseBudget}
              />
              <AgentBudgetButton
                title="Preview"
                variant="outlined"
                className="flex items-center gap-4"
                icon={EyeSVG}
                onClick={() => {
                  togglePanel &&
                    togglePanel({
                      panelName: "showUrlPreviewPanel",
                    });
                }}
              />
            </div>
          </div>
        ) : null}

        {/* Subagent Button */}
        {message.function_name != "rollback" && (
          <div
            className={cn(
              "transition-opacity duration-200 h-[25px] max-w-4xl items-center flex justify-between mx-auto",
              isHovered ? "opacity-100" : "opacity-100"
            )}
          >
            <div className="flex justify-between items-center text-[#5C5F66]">
              {!isSubagentActive && message.subagent_trajectory?.length > 0 && (
                <SubagentButton
                  handleSubagentClick={handleSubagentClick}
                  message={message}
                  agentState={agentState}
                  enableLoading={false}
                  runningState={false}
                  panelState={panelState}
                  isSelected={selectedMessageId == message.id}
                  jobDetails={jobDetails}
                  agentName={agentName}
                  onPause={onPause}
                  isPauseLoading={isPauseLoading}
                />
              )}
              {!message?.subagent_trajectory && !currentChunk && (
                <span className="ml-14" />
              )}
              {isLastMessage &&
                isSubagentActive &&
                message.subagent_trajectory?.length > 0 && (
                  <SubagentButton
                    handleSubagentClick={handleSubagentClick}
                    message={message}
                    agentState={agentState}
                    enableLoading={true}
                    runningState={isSubagentActive}
                    panelState={panelState}
                    isSelected={selectedMessageId === message.id}
                    jobDetails={jobDetails}
                    agentName={agentName}
                    onPause={onPause}
                    isPauseLoading={isPauseLoading}
                  />
                )}
            </div>

            {/* TimeStamp & Actions */}
            {message.function_name != "rollback" && (
              <div className="text-[#5C5F66]">
                <div className="relative flex items-center justify-end min-w-[200px]">
                  <span
                    className={cn(
                      "text-[13px] md:text-[15px] leading-[20px] transition-opacity hidden md:block  md:absolute   md:right-0",
                      isHovered
                        ? "opacity-0 pointer-events-none"
                        : "opacity-100"
                    )}
                  >
                    {formatMessageTimestamp(message.timestamp)}
                  </span>
                  <div
                    className={cn(
                      "relative z-20",
                      !agentState?.agent_running
                        ? "md:opacity-100 pointer-events-none"
                        : ""
                    )}
                  >
                    <MessageActions
                      message={message}
                      isHovered={isHovered}
                      forkStatus={forkStatus}
                      isCloudFlow={isCloudFlow}
                      hideImportantActions={hideImportantActions}
                      handleRollback={handleRollback}
                      podIsPaused={podIsPaused}
                      className={cn(
                        "inline-flex items-center transition-opacity absolute right-0",
                        isHovered
                          ? "opacity-100"
                          : "opacity-0 pointer-events-none"
                      )}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Finish Action , Credit Recharged , Budget Increased */}

        {!isSubagent && (
          <BottomStatusSeparators
            message={message as any}
            credits={credits}
            max_budget={max_budget}
          />
        )}

        {isGitHubPushModalOpen && !podIsPaused && (
          <GitHubPushModal
            podIsPaused={podIsPaused}
            lastGithubUsed={lastGithubUsed}
            isOpen={isGitHubPushModalOpen}
            onOpenChange={(open) => setIsGitHubPushModalOpen(open)}
            jobId={message.containerId}
            onSuccess={() => setIsGitHubPushModalOpen(false)}
          />
        )}
      </div>
    );
  }
);
