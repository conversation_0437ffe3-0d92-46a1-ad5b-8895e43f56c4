import { useCredits } from '@/contexts';
import { Loader2 } from 'lucide-react';
import CopperCoin from '@/assets/copper-coin.svg';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CreditsPopoverContent } from './CreditsPopoverContent';

export function CreditsDisplay() {
  const { credits, loading, error } = useCredits();

  if (loading) {
    return (
      <div className="hidden md:flex items-center bg-[#111111] rounded-full px-4 py-2">
        <Loader2 className="h-4 w-4 mr-2 text-[#F3CA5F] animate-spin" />
        <span className="text-[#8A8B91] text-sm">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="hidden md:flex items-center bg-[#111111] rounded-full px-4 py-2">
        <span className="text-sm text-destructive">Error loading credits</span>
      </div>
    );
  }

  return (
    <Popover>
      <PopoverTrigger className='hidden md:flex' asChild>
        <div className="flex items-center space-x-2 bg-[#F3CA5F]/[.10] rounded-full px-[10px] py-2 cursor-pointer hover:bg-[#F3CA5F]/[.20] transition-colors">
          <img alt='Balance Coin' src={CopperCoin} className='w-5 h-5' />
          <div className="flex items-center space-x-1">
            <span className="text-[#F3CA5F] text-sm font-medium">
              {credits.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[350px] p-0 border-[#2E2F34] rounded-xl bg-transparent shadow-xl mt-3" align="end">
        <CreditsPopoverContent />
      </PopoverContent>
    </Popover>
  );
}
