import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader } from './ui/card';
import { agentApi } from '@/services/agentApi';
import CoinSVG from "@/assets/copper-coin.svg";
import DeployedCard, { type IDeployment } from './DeployedCard';
import WebImage from "@/assets/webImage.svg";


export default function DeployedApps({handleJobClick}:{
  handleJobClick: (job: any) => void;
}) {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [deployments, setDeployments] = useState<IDeployment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Function to fetch deployments
  const fetchDeployments = useCallback(async () => {
    try {
      setLoading(true);
      const response = await agentApi.getDeployments();

      if ('error' in response) {
        setDeployments([]);
      } else {
        // Map API response to component's expected format
        const mappedDeployments: IDeployment[] = response.map((deployment: any) => ({
          id: deployment.job_id,
          app_name: deployment.app_name || 'Unnamed App',
          deployStatus: deployment.status,
          deployUrl: deployment.deployed_url || '',
          custom_domain: deployment.custom_domain || '',
          latest_run: {
            status: deployment.status,
            updated_at: deployment.deployed_at || null,
          },
          description: deployment.task || 'No task available',
          deployment_id: deployment.deployment_id,
        }));

        const sortedDeployments = mappedDeployments.sort((a, b) => {
          const timeA = new Date(a.latest_run.updated_at).getTime();
          const timeB = new Date(b.latest_run.updated_at).getTime();
          return timeB - timeA;
        });

        setDeployments(sortedDeployments.splice(0, 5));
      }
    } catch (err: any) {
      console.error('Error fetching deployments:', err);
      setDeployments([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch deployments on component mount
  useEffect(() => {
    fetchDeployments();
  }, [fetchDeployments]);

  const getJobDetails = async(deployment: IDeployment)=>{

    try{
      const response = await agentApi.getJob(deployment.id);
      const job = response.data;
      handleJobClick(job);
    }catch{}
  }


  // Create placeholder cards for empty slots
  const emptyCards = Array(6 - Math.min( deployments.length, 6)).fill(null);


  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col mt-2 mb-8 space-y-5 min-h-[546px] bg-[#0F0F10] md:border-[#252629] md:border p-8 rounded-[16px]">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {Array(6).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="bg-[#111112] border-[#FFFFFF0F] h-[300px] animate-pulse">
              <CardHeader className="p-5">
                <div className="h-4 w-24 bg-[#FFFFFF14] rounded"></div>
              </CardHeader>
              <CardContent className="p-5 pt-0">
                <div className="h-4 w-full bg-[#FFFFFF14] rounded mb-4"></div>
                <div className="h-4 w-3/4 bg-[#FFFFFF14] rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col relative mt-2 mb-8 space-y-5 max-h-[650px] overflow-y-scroll min-h-[546px] bg-[#0F0F10] md:border-[#252629] md:border md:p-6 overflow-clip rounded-[16px]">

      {deployments.length === 0 ? (
        <div className="md:flex flex-col space-y-5 md:border-[#252629] md:border rounded-[16px]">
          <div className="hidden grid-cols-1 gap-6 md:grid md:grid-cols-3">
            {Array(6).fill(null).map((_, index) => (
              <Card key={`empty-grid-${index}`} className="bg-[#111112] border-[#FFFFFF0F] hidden md:h-[300px]">
                <CardHeader className="p-5">
                  <div className="h-4 w-24 bg-[#FFFFFF14] rounded"></div>
                </CardHeader>
                <CardContent className="p-5 pt-0">
                  <div className="h-4 w-full bg-[#FFFFFF14] rounded mb-4"></div>
                  <div className="h-4 w-3/4 bg-[#FFFFFF14] rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="absolute  rounded-[16px] backdrop-blur-[2px] left-0 right-0 bottom-0 top-0 h-full md:border md:border-[#242424]/60 flex flex-col items-center justify-center text-center">
            <div className="mb-4">
              <img src={WebImage} alt="Globe" className="w-[56px] h-[56px] mx-auto" />
            </div>
            <h3 className="mb-2 text-[24px] font-medium text-white/80">0 apps deployed</h3>
            <p className="max-w-md text-white/30 font-medium text-[16px]">
              Deploy your application to a production-ready environment.This will make it publicly accessible with managed infrastructure
            </p>
            <div className="mt-7 bg-[#242119] rounded-[12px] p-[14px] flex items-center gap-2">
              <img src={CoinSVG} alt="Credits" className="w-5 h-5" />
              <span className="text-[#F3CA5F] text-[15px] font-medium font-['Inter']"><span className='text-[#E6D9B8]'>Deployment costs</span> 50 credits/month</span>
            </div>
          </div>
        </div>
      ) : (
        <div className="grid h-full grid-cols-1 gap-6 md:grid-cols-3">
          {deployments.map((deployment, index) => (
            <DeployedCard
              key={deployment.id}
              deployment={deployment}
              index={index}
              hoveredCard={hoveredCard}
              onHoverStart={setHoveredCard}
              onHoverEnd={() => setHoveredCard(null)}
              onClick={getJobDetails}
            />
          ))}

          {/* Empty placeholder cards */}
          {emptyCards.map((_, index) => (
            <Card
              key={`empty-${index}`}
              className="bg-[#101011] hidden md:block border-[#282829] h-[290px] md:border-dotted"
            >
              {/* Empty card */}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
