import { useState } from "react";
import Footer from "./Footer";
import RetroGrid from "./RetroGrid";
import AuthModal from "./modals/AuthModal";
import { useAuth } from "@/contexts";

export const DownloadSection = () => {
    // Hooks
    const { user, session } = useAuth();

    // State
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [openView, setOpenView] = useState<"login" | "signup">("login");

    const scrollToTop = () => {
        window.requestAnimationFrame(() => {
            const mainTaskInput = document.getElementById('mainTaskInput');
            if (mainTaskInput) {
                mainTaskInput.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
                setTimeout(() => {
                    (mainTaskInput as HTMLInputElement).focus();
                }, 500); // Give time for scroll to complete before focusing
            }
        });
    }


    const handleGetStartedClick = (e: any) => {
        e.preventDefault();

        if (user && session) {
            scrollToTop();
        } else {
            setIsModalOpen(true);
        }
    };

    return (
        <>
            <AuthModal
                enableWelcomeModal={true}
                open={isModalOpen}
                onOpenChange={setIsModalOpen}
                defaultView={"login"}
                openView={openView}
                onSuccess={() => {
                    setOpenView("login");
                    setIsModalOpen(false);
                }}
            />
            <section className="shadow-xl overflow-hidden md:py-20 pb-[15rem] md:pb-[20rem] mt-[10rem] relative">
                <RetroGrid
                    height='70%'
                    top="30%"
                    gridSizeX={50}
                    gridSizeY={30}
                    gridLineWidth={0.3}
                    gridOpacity={0.6}
                />
                <div className="relative z-20 flex flex-col items-center justify-center w-full h-full">
                    <div className="flex flex-col items-center">
                        <div className="flex flex-col items-center md:gap-4">
                            <h2 className="text-[40px] md:text-[80px] md:leading-[80px] font-medium text-white">
                                Start building with
                            </h2>
                            <h2 className="text-[#00FF66] font-pixel text-[34px]  md:text-[72px] md:leading-[72px] uppercase">Emergent Today</h2>
                        </div>
                        <button
                            onClick={handleGetStartedClick}
                            className="relative flex items-center gap-3 py-4 text-base font-semibold leading-6 tracking-wide text-black transition-colors bg-white rounded-full mt-14 px-14 hover:bg-gray-100 get-started-button before:absolute before:inset-0 before:-m-1 before:rounded-full before:border before:border-[#FFFFFF26] before:-z-10"
                        >
                            {user && session ? "Keep Building" : "Get Started"}
                        </button>
                    </div>
                </div>
                <Footer />
            </section>
        </>
    );
};

export default DownloadSection;