import { useState, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Upload, X, Image as ImageIcon } from "lucide-react";
import * as Tooltip from "@radix-ui/react-tooltip";
import { Info } from "lucide-react";

interface ImageData {
  mime_type: string;
  img_base64: string;
  originalFile: File;
}

interface ImageSelectorProps {
  selectedImages: ImageData[];
  setSelectedImages: (images: ImageData[]) => void;
  label?: string;
  description?: string;
  inputId?: string;
}

const InfoTooltip = ({ description }: { description: string }) => (
  <Tooltip.Root>
    <Tooltip.Trigger asChild>
      <button className="inline-flex items-center ml-2 text-[#666] hover:text-[#999] focus:outline-none">
        <Info size={14} />
      </button>
    </Tooltip.Trigger>
    <Tooltip.Portal>
      <Tooltip.Content
        className="max-w-xs bg-[#1A1A1A] text-[#DDDDE6] text-sm px-4 py-2.5 rounded-lg shadow-lg z-[9999]"
        sideOffset={5}
      >
        {description}
        <Tooltip.Arrow className="fill-[#1A1A1A]" />
      </Tooltip.Content>
    </Tooltip.Portal>
  </Tooltip.Root>
);

const ImageSelector = ({
  selectedImages,
  setSelectedImages,
  label = "Images",
  description = "Select images to include with your task. Images will be reduced in quality by 50% and converted to base64 format.",
  inputId
}: ImageSelectorProps) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const processImage = useCallback(async (file: File): Promise<ImageData | null> => {
    return new Promise((resolve) => {
      try {
        const reader = new FileReader();
        
        reader.onload = (e) => {
          if (!e.target?.result) {
            console.error('Failed to read file');
            resolve(null);
            return;
          }
          
          const img = new Image();
          img.onload = () => {
            try {
              // Create a canvas to resize the image
              const canvas = document.createElement('canvas');
              const scaleFactor = 0.7; // Reduce dimensions by 30%
              canvas.width = img.width * scaleFactor;
              canvas.height = img.height * scaleFactor;
              
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                console.error('Failed to get canvas context');
                resolve(null);
                return;
              }
              
              // Draw the image on the canvas with reduced dimensions
              ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
              
              // Get the resized image as a blob with reduced quality
              canvas.toBlob(
                (blob) => {
                  if (!blob) {
                    console.error('Failed to create blob from canvas');
                    resolve(null);
                    return;
                  }
                  
                  // Use FileReader to convert blob to base64
                  const blobReader = new FileReader();
                  blobReader.onload = () => {
                    const base64Result = blobReader.result as string;
                    // Extract just the base64 data (remove the data URL prefix)
                    const base64String = base64Result.substring(base64Result.indexOf(',') + 1);
                    
                    resolve({
                      mime_type: file.type,
                      img_base64: base64String,
                      originalFile: file
                    });
                  };
                  
                  blobReader.onerror = () => {
                    console.error('Error reading blob');
                    resolve(null);
                  };
                  
                  blobReader.readAsDataURL(blob);
                },
                file.type,
                0.7 // Quality setting (0-1)
              );
            } catch (error) {
              console.error('Error processing image:', error);
              resolve(null);
            }
          };
          
          img.onerror = () => {
            console.error('Error loading image');
            resolve(null);
          };
          
          img.src = e.target.result as string;
        };
        
        reader.onerror = () => {
          console.error('Error reading file');
          resolve(null);
        };
        
        reader.readAsDataURL(file);
      } catch (error) {
        console.error('Unexpected error in processImage:', error);
        resolve(null);
      }
    });
  }, []);

  const handleImageSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    setIsProcessing(true);
    
    try {
      const imagePromises = Array.from(files).map(file => processImage(file));
      const processedImages = await Promise.all(imagePromises);
      
      // Filter out null results and add valid images to the existing selection
      const validImages = processedImages.filter((img): img is ImageData => img !== null);
      setSelectedImages([...selectedImages, ...validImages]);
    } catch (error) {
      console.error('Error processing images:', error);
    } finally {
      setIsProcessing(false);
      // Reset the input value to allow selecting the same file again
      event.target.value = '';
    }
  }, [selectedImages, setSelectedImages, processImage]);

  const removeImage = useCallback((index: number) => {
    setSelectedImages(selectedImages.filter((_, i) => i !== index));
  }, [selectedImages, setSelectedImages]);

  return (
    <div className="space-y-3">
      <p className="text-[#999] mb-2 font-['Inter']">
        {label}
        {description && <InfoTooltip description={description} />}
      </p>
      
      <div className="flex flex-wrap gap-3 mb-3">
        {selectedImages.map((image, index) => (
          <div 
            key={index} 
            className="relative w-24 h-24 rounded-md overflow-hidden border border-[#333] group"
          >
            <img 
              src={`data:${image.mime_type};base64,${image.img_base64}`} 
              alt={`Selected image ${index + 1}`}
              className="w-full h-full object-cover"
            />
            <button
              onClick={() => removeImage(index)}
              className="absolute top-1 right-1 bg-black/70 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              aria-label="Remove image"
            >
              <X size={14} className="text-white" />
            </button>
            <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs py-1 px-2 truncate">
              {image.originalFile.name}
            </div>
          </div>
        ))}
        
        <div className="w-24 h-24 flex flex-col items-center justify-center border border-dashed border-[#333] rounded-md hover:border-[#666] transition-colors">
          <label className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
            <input 
              type="file" 
              accept="image/*" 
              multiple 
              onChange={handleImageSelect} 
              className="hidden" 
              disabled={isProcessing}
              id={inputId}
            />
            {isProcessing ? (
              <Loader2 className="h-6 w-6 text-[#999] animate-spin" />
            ) : (
              <>
                <ImageIcon className="h-6 w-6 text-[#999] mb-1" />
                <span className="text-xs text-[#999]">Add Images</span>
              </>
            )}
          </label>
        </div>
      </div>
    </div>
  );
};

export default ImageSelector;
