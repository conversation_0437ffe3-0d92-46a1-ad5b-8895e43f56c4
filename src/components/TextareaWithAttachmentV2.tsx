import React, { useState, useEffect, useRef } from 'react';
import { Textarea } from "@/components/ui/textarea";
import * as Tooltip from "@radix-ui/react-tooltip";
import { Button } from './ui/button';
import { Loader2, Check, ChevronDown } from 'lucide-react';
import { BorderBeam } from './ui/border-beam';
import { createPortal } from 'react-dom';

import { motion } from 'motion/react';

// Assets
import SubmitArrow from '@/assets/submit-arrow.svg';
import SettingInactive from '@/assets/setting-inactive.svg';
import SettingActive from '@/assets/setting-active.svg';
import CopyIcon from "@/assets/copy-paperclip.svg"

import { cn } from '@/lib/utils';
import GitHubButtonForm from './github/GithubButtonForm';
import RobotIcon from "@/assets/uil_robot.svg";
import { TooltipProvider } from './ui/tooltip';

interface TextareaWithAttachmentProps {
  value: string;
  placeholder?: string;
  disabled: boolean;
  attachIcon: string;
  attachTooltip?: string;
  githubTooltip?: string;
  className?: string;
  isSubmitting?: boolean;
  showControls?: boolean;
  maxHeight?: string;
  showGithubSettings?: boolean;
  handleSettingsClick?: () => void;
  handleGithubSettings?: () => void;
  onPaste?: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void;
  onAttachClick: () => void;
  onSubmit: () => void;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  hasUserConnectedGithub?: boolean;
  githubUrl?: string;
  onClearGithubUrl: () => void;
  handleExperimentalChange: (value: boolean) => void;
  experimentalEnabled?: boolean;
}

const TextareaWithAttachmentV2: React.FC<TextareaWithAttachmentProps> = ({
  value,
  onChange,
  placeholder = '',
  maxHeight = '200px',
  onAttachClick,
  onSubmit,
  disabled,
  onPaste,
  attachIcon,
  attachTooltip,
  githubTooltip,
  isSubmitting,
  handleSettingsClick,
  handleGithubSettings,
  showGithubSettings,
  showControls,
  className,
  hasUserConnectedGithub,
  githubUrl,
  onClearGithubUrl,
  handleExperimentalChange,
  experimentalEnabled: externalExperimentalEnabled
}) => {
  // Typing effect state
  const [displayText, setDisplayText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [loopNum, setLoopNum] = useState(0);

  // Drag and drop state
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  // Model selection state - use external prop if provided, otherwise use localStorage
  const [internalExperimentalEnabled, setInternalExperimentalEnabled] = useState<boolean>(
    externalExperimentalEnabled !== undefined
      ? externalExperimentalEnabled
      : localStorage.getItem("experimentalEnabled") === "true"
  );

  // Use external prop if provided, otherwise use internal state
  const experimentalEnabled = externalExperimentalEnabled !== undefined ? externalExperimentalEnabled : internalExperimentalEnabled;

  // Sync internal state with external prop when it changes
  useEffect(() => {
    if (externalExperimentalEnabled !== undefined) {
      setInternalExperimentalEnabled(externalExperimentalEnabled);
    }
  }, [externalExperimentalEnabled]);

  useEffect(() => {
    if(experimentalEnabled) {
      handleExperimentalChange(true);
    } else {
      handleExperimentalChange(false);
    }
  }, [experimentalEnabled]);

  // Dropdown state
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  // refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const modelDropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const typingSpeed = 100; // Speed of typing in ms
  const deletingSpeed = 50; // Speed of deleting in ms
  const delayAfterPhrase = 2000; // How long to pause after completing a phrase

  // The phrases that will be displayed
  const phrases = [
    'a dashboard for...',
    'a beautiful landing page for...',
    'an app for...',
    'a clone of netflix...',
    'a game where...'
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      handleTyping();
    }, isDeleting ? deletingSpeed : typingSpeed);

    return () => clearTimeout(timer);
  }, [displayText, isDeleting]);

  // Save experimentalEnabled to localStorage when it changes (only when using internal state)
  useEffect(() => {
    if (externalExperimentalEnabled === undefined) {
      localStorage.setItem('experimentalEnabled', JSON.stringify(internalExperimentalEnabled));
    }
  }, [internalExperimentalEnabled, externalExperimentalEnabled]);

  // Calculate dropdown position
  const calculateDropdownPosition = () => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const dropdownHeight = 120; // Approximate height of dropdown
    const dropdownWidth = 180; // Width of dropdown
    const spacing = 8; // Space between button and dropdown

    // Position below the button by default
    let top = buttonRect.bottom + spacing;
    let left = buttonRect.left;

    // Check if dropdown would go off the bottom of the screen
    if (top + dropdownHeight > window.innerHeight) {
      // Position above the button instead
      top = buttonRect.top - dropdownHeight - spacing;
    }

    // Check if dropdown would go off the right side of the screen
    if (left + dropdownWidth > window.innerWidth) {
      left = window.innerWidth - dropdownWidth - 16; // 16px margin from edge
    }

    // Check if dropdown would go off the left side of the screen
    if (left < 0) {
      left = 16; // 16px margin from edge
    }

    setDropdownPosition({ top, left });
  };

  // Close dropdown when clicking outside and handle scroll/resize
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!modelDropdownRef.current || !buttonRef.current) return;

      const target = event.target as Node;
      // Check if the click is outside both the dropdown and button
      if (showModelDropdown &&
          !modelDropdownRef.current.contains(target) &&
          !buttonRef.current.contains(target)) {
        setShowModelDropdown(false);
      }
    };

    const handleScrollOrResize = () => {
      if (showModelDropdown) {
        calculateDropdownPosition();
      }
    };

    // Add the event listeners only when the dropdown is open
    if (showModelDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScrollOrResize, true);
      window.addEventListener('resize', handleScrollOrResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScrollOrResize, true);
      window.removeEventListener('resize', handleScrollOrResize);
    };
  }, [showModelDropdown]);

  const handleTyping = () => {
    // Current phrase based on the loop number
    const currentPhrase = phrases[loopNum % phrases.length];

    // Set the new display text based on whether we're deleting or typing
    if (!isDeleting && displayText === currentPhrase) {
      // If we've completed typing the current phrase, pause then delete
      setTimeout(() => {
        setIsDeleting(true);
      }, delayAfterPhrase);
      return;
    } else if (isDeleting && displayText === '') {
      // If we've deleted the entire phrase, move to the next phrase
      setIsDeleting(false);
      setLoopNum(loopNum + 1);
      return;
    }

    // Update the display text
    setDisplayText(
      isDeleting
        ? currentPhrase.substring(0, displayText.length - 1)
        : currentPhrase.substring(0, displayText.length + 1)
    );
  };

  // Handle textarea resizing
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';

      // Calculate new height (min of scrollHeight and maxHeight)
      const newHeight = Math.min(textarea.scrollHeight, parseInt(maxHeight));
      textarea.style.height = `${newHeight}px`;
    }
  }, [value, maxHeight]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter creates a new line - default behavior, no need to do anything
      } else {
        // Enter submits the form
        e.preventDefault(); // Prevent default behavior (new line)
        if (!disabled && !isSubmitting) {
          onSubmit();
        }
      }
    }
  };

  // Handle file drop
  const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);

    // Process dropped files
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      // Create a custom event that simulates a file input change
      // This allows us to handle the dropped files without opening the file picker
      const customEvent = new Event('custom-file-drop', { bubbles: true });

      // Add the files to the event
      Object.defineProperty(customEvent, 'files', {
        value: files,
        writable: false
      });

      // Dispatch the event on the document
      document.dispatchEvent(customEvent);

      // Also call onAttachClick to maintain compatibility with existing code
      // that might be listening for this event
      if (onPaste) {
        // Create a clipboard event-like object with the files
        const pasteEvent = {
          clipboardData: {
            files: files,
            items: Array.from(files).map(file => ({
              kind: 'file',
              type: file.type,
              getAsFile: () => file
            }))
          },
          preventDefault: () => {}
        } as unknown as React.ClipboardEvent<HTMLTextAreaElement>;

        // Call the onPaste handler with our custom event
        onPaste(pasteEvent);
      } else {
        // Fallback to the attachment click handler if no paste handler is available
        onAttachClick();
      }
    }
  };

  return (
    <div
      className={`${className} relative rounded-[18px] overflow-clip ${
        isDraggingOver ? "ring-2 ring-[#80FFF9] ring-opacity-70" : ""
      } bg-[#141415] flex flex-col justify-between`}
      onDragOver={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onDragEnter={(e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDraggingOver(true);
      }}
      onDragLeave={(e) => {
        e.preventDefault();
        e.stopPropagation();
        // Only set to false if we're leaving the main element (not entering a child)
        if (e.currentTarget.contains(e.relatedTarget as Node)) {
          return;
        }
        setIsDraggingOver(false);
      }}
      onDrop={handleFileDrop}
    >
      {/* Drag overlay */}
      {isDraggingOver && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-[#141415]/90 rounded-[18px]">
          <div className="text-[#80FFF9] text-center">
            <p className="font-medium font-brockmann">Drop files here</p>
            <p className="text-sm text-[#80FFF9]/70">
              JPEG, PNG (max 5MB)
            </p>
          </div>
        </div>
      )}

      {/* Main textarea */}
      <Textarea
        id="mainTaskInput"
        ref={textareaRef}
        value={value}
        onChange={onChange}
        onKeyDown={handleKeyDown}
        placeholder=""
        className="h-full w-full bg-[#141415] rounded-t-[18px] text-[16px] resize-none min-h-[80px] md:min-h-[100px] p-3  md:p-4 border-0 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 caret-[#80FFF9] font-brockmann font-medium overflow-auto"
        onPaste={onPaste}
        style={{
          maxHeight: maxHeight,
          transition: "height 0.1s ease-out",
        }}
      />

      {/* Typing effect placeholder */}
      {value === "" && (
        <div className="absolute top-0 left-0 p-4 pointer-events-none text-gray-500 font-brockmann font-medium text-[16px]">
          <span>Build me {displayText}</span>
        </div>
      )}

      {/* Bottom controls container */}
      <div className="flex items-center justify-between w-full p-2 md:p-3  bg-[#141415] ">
        {/* Attachment button with tooltip */}
        <div className="flex flex-row items-center gap-2">
          <div className="rounded-lg bg-none">
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <Button
                  className={cn(
                    "p-2 md:px-3 md:py-2 pointer-events-auto rounded-[30px] flex items-center group/paperclip justify-center h-[32px] md:h-[40px] hover:bg-white/10 bg-white bg-opacity-5 "
                  )}
                  type="button"
                  onClick={onAttachClick}
                >
                  <img
                    src={CopyIcon}
                    alt="Attach"
                    className="transition-transform duration-200 transform min-w-4 min-h-4 md:min-w-5 md:min-h-5 group-hover/paperclip:rotate-45 "
                  />
                </Button>
              </Tooltip.Trigger>
              {attachTooltip && (
                <Tooltip.Portal>
                  <Tooltip.Content
                    className="max-w-xs bg-[#fff] text-[#0f0f10] font-medium tracking-[-0.3px] text-sm px-4 py-2.5 rounded-lg shadow-lg z-[9999]"
                    sideOffset={5}
                    asChild
                  >
                    <motion.div
                      initial={{ opacity: 0, y: -2, scale: 1 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 1 }}
                      transition={{
                        type: "easeOut",
                        stiffness: 400,
                        damping: 25,
                        mass: 0.5,
                        duration: 0.4,
                      }}
                    >
                      {attachTooltip}
                      <Tooltip.Arrow className="fill-[#fff]" />
                    </motion.div>
                  </Tooltip.Content>
                </Tooltip.Portal>
              )}
            </Tooltip.Root>
          </div>
          <div className="relative rounded-lg bg-none">
            <GitHubButtonForm
              isConnected={hasUserConnectedGithub || !!githubUrl}
              isActive={showGithubSettings || !!githubUrl}
              githubUrl={githubUrl}
              githubTooltip={githubTooltip}
              handleGithubSettings={handleGithubSettings || (() => {})}
              onClearGithubUrl={onClearGithubUrl}
            />
          </div>
          <div className="relative rounded-lg bg-none">
            <button
              ref={buttonRef}
              type="button"
              className={cn(
                "p-2 md:px-3 max-h-[32px] md:max-h-[44px] flex items-center gap-1 transition-colors duration-200 rounded-[30px] bg-white/5 hover:bg-white/10 ",
                showModelDropdown && "bg-[#5CD2E51A]"
              )}
              onClick={(e) => {
                e.stopPropagation();
                if (!showModelDropdown) {
                  calculateDropdownPosition();
                }
                setShowModelDropdown(!showModelDropdown);
              }}
            >
              <img
                src={RobotIcon}
                alt="Robot"
                className="hidden w-4 h-4 transition-transform duration-200 transform md:block md:w-6 md:h-6"
              />
              <span
                className={cn(
                  "text-[#E6E6E6] ml-1 text-[12px]  md:text-[14px] flex flex-nowrap  whitespace-nowrap"
                )}
              >
                E-1
                {experimentalEnabled ? (
                  <span>
                    .1
                  </span>
                ) : (
                  ""
                )}
              </span>
              <ChevronDown
                className={`w-4 h-4 md:ml-2 transition-transform duration-200 ${
                  showModelDropdown ? "rotate-180" : ""
                }`}
              />
            </button>
          </div>

          {/* Custom dropdown rendered in portal */}
          {showModelDropdown &&
            createPortal(
              <div
                ref={modelDropdownRef}
                className="fixed z-[9999] w-[180px] bg-[#131314] border border-[#242424] rounded-md shadow-lg overflow-y-auto model-dropdown"
                style={{
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                }}
              >
                <div className="flex flex-col gap-[6px] p-2">
                  <div
                    className={cn(
                      "px-3 md:min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                      !experimentalEnabled && "bg-[#172426] text-[#66EAFF]"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      setInternalExperimentalEnabled(false);
                      handleExperimentalChange(false);
                      setShowModelDropdown(false);
                    }}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span className="text-[12px] md:text-[16px]">E-1</span>

                      {!experimentalEnabled && (
                        <Check className="h-4 w-4 text-[#66EAFF]" />
                      )}
                    </div>
                  </div>
                  <div
                    className={cn(
                      "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                      experimentalEnabled && "bg-[#172426] text-[#66EAFF]"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      setInternalExperimentalEnabled(true);
                      handleExperimentalChange(true);
                      setShowModelDropdown(false);
                    }}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span className="flex items-center text-[12px] md:text-[16px]">
                        E-1.1
                      </span>
                    </div>
                  </div>
                </div>
              </div>,
              document.body
            )}
        </div>

        {/* Submit button */}
        <div className="flex items-center gap-3">
          <TooltipProvider>
            <Tooltip.Root delayDuration={0}>
              <Tooltip.Trigger asChild>
                <Button
                  className={cn(
                    "p-2 md:px-3 md:py-2 rounded-[30px] flex items-center justify-center h-[32px] md:h-[40px] hover:bg-white/10  ",
                    showControls
                      ? "bg-[#66EAFF0D] border-[#66EAFF1F] border-[1px]"
                      : "bg-white bg-opacity-5"
                  )}
                  type="button"
                  onClick={handleSettingsClick}
                >
                  <img
                    src={showControls ? SettingActive : SettingInactive}
                    alt="Settings"
                    className="min-w-4 min-h-4 md:min-w-5 md:min-h-5"
                  />
                </Button>
              </Tooltip.Trigger>
              <Tooltip.Content
                className="max-w-xs bg-[#fff] text-[#0f0f10] font-medium tracking-[-0.3px] text-sm px-4 py-2.5 rounded-lg shadow-lg z-[9999]"
                sideOffset={5}
                asChild
              >
                <motion.div
                  initial={{ opacity: 0, y: -2, scale: 1 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 1 }}
                  transition={{
                    type: "easeOut",
                    stiffness: 400,
                    damping: 25,
                    mass: 0.5,
                    duration: 0.4,
                  }}
                >
                  Advanced Controls
                  <Tooltip.Arrow className="fill-[#fff]" />
                </motion.div>
              </Tooltip.Content>
            </Tooltip.Root>
          </TooltipProvider>

          <Button
            disabled={disabled}
            className="p-2 md:px-[12px] md:py-[8px] rounded-[30px] h-[32px] md:h-[36px] w-[44px] md:w-[56px] text-white transition-colors bg-[#ECECEC] flex items-center justify-center"
            onClick={onSubmit}
            type="button"
          >
            <div className="flex items-center justify-center w-full">
              {isSubmitting ? (
                <Loader2 className="w-5 h-5 text-black animate-spin" />
              ) : (
                <img
                  src={SubmitArrow}
                  alt="Submit"
                  className="min-w-5 min-h-5 max-w-5 max-h-5 md:min-w-6 md:min-h-6 md:max-w-6 md:max-h-6"
                />
              )}
            </div>
          </Button>
        </div>
      </div>
      <BorderBeam
        size={250}
        duration={12}
        delay={9}
        colorFrom="#80FFF9"
        colorTo="transparent"
      />
    </div>
  );
};

export default TextareaWithAttachmentV2;