import { useState } from 'react';
import { Button } from './ui/button';
import { UpgradeTierModal } from './modals/UpgradeTierModal';
// import { SparklesIcon } from 'lucide-react';
import StarsSVG from "@/assets/Stars.svg";

interface UpgradeTierButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'subtle';
  size?: 'sm' | 'md' | 'lg';
}

export function UpgradeTierButton({ 
  className = '', 
  variant = 'default',
  size = 'md'
}: UpgradeTierButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const getButtonStyle = () => {
    switch (variant) {
      case 'outline':
        return 'border border-[#F3CA5F] text-[#F3CA5F] hover:bg-[#F3CA5F]/10';
      case 'subtle':
        return 'bg-[#F3CA5F]/10 text-[#F3CA5F] hover:bg-[#F3CA5F]/20';
      default:
        return 'bg-[#F3CA5F] text-black hover:bg-[#E7A93C]';
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2';
    }
  };


    const buttonStyle = {
    background: `
      linear-gradient(180deg, #ffffff, #000000),
      #F3CA5F
    `,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundBlendMode: 'overlay, normal',
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        style={buttonStyle}
        className={`${getButtonStyle()} ${getButtonSize()} flex items-center font-semibold font-brockmann p-[12px] px-4 justify-center rounded-lg tracking-[-0.2px] gap-2 ${className}`}
      >
        <img src={StarsSVG} alt="right arrow" className="w-6 h-6" />
        <span>Upgrade your Tier</span>
      </button>
      
      <UpgradeTierModal 
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
      />
    </>
  );
}
