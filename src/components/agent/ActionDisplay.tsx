import { parseBulkFileCreatorAction } from "@/lib/utils/parseFileContent";
import { BulkFileViewer } from "../BulkFileViewer";

interface ActionDisplayProps {
  action: string;
  functionName?: string;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  isSubagent?: boolean;
}

export const ActionDisplay = ({ 
  action, 
  functionName, 
  isExpanded, 
  setIsExpanded,
  isSubagent = false 
}: ActionDisplayProps) => {
  
  const truncateAction = (action: string) => {
    const maxLength = 50;
    return action.length > maxLength ? `${action.substring(0, maxLength)}...` : action;
  };

  const getDisplayAction = () => {
    if (!action) return "";

    if (functionName && ["str_replace_editor", "create_file", "view_file", "search_replace", "insert_text"].includes(functionName)) {
      const [_, subCmd, filePath] = action.split(" ");

      if (subCmd === "view") {
        return (
          <span className="flex items-center gap-2 font-mono">
            Viewed{" "}
            <span className="text-[#DD99FF] font-brockmann">{filePath}</span>
          </span>
        );
      } else if (subCmd === "str_replace") {
        return (
          <span className="flex items-center gap-2 font-mono">
            Edited{" "}
            <span className="text-[#DD99FF] font-brockmann">{filePath}</span>
          </span>
        );
      } else if (subCmd === "create") {
        const filePath = action.split(" ")[2];
        return (
          <span className="flex items-center gap-2 font-mono">
            Created{" "}
            <span className="text-[#DD99FF] font-brockmann">{filePath}</span>
          </span>
        );
      }
    } else if (action.startsWith("bulk_file_creator")) {
      try {
        const files = parseBulkFileCreatorAction(action);

        return isExpanded ? (
          <div className="flex flex-col space-y-2">
            <div className="text-[#E5E5E5] font-medium">
              <span className="text-[#DD99FF]">Created</span> {files.length}{" "}
              {files.length === 1 ? "file" : "files"}
            </div>
            <BulkFileViewer files={files} isSubagent={isSubagent} />
          </div>
        ) : (
          <div
            className="flex flex-col w-full space-y-1 cursor-pointer"
            onClick={() => setIsExpanded(true)}
          >
            <div className="text-[#E5E5E5] font-medium">
              <span className="text-[#DD99FF]">Created</span> {files.length}{" "}
              {files.length === 1 ? "file" : "files"}
            </div>
          </div>
        );
      } catch (error) {
        console.error("Error parsing bulk file creator action:", error);
        return (
          <span className="text-red-500">Error parsing file content</span>
        );
      }
    }
    return isExpanded ? action : truncateAction(action);
  };

  return (
    <code
      className="text-[#E5E5E5] font-mono text-sm md:w-full"
    >
      {getDisplayAction()}
    </code>
  );
};