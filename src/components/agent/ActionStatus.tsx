import { Spinner } from "@/components/ui/spinner";

interface ActionStatusProps {
  isRunning: boolean;
  envSuccess?: boolean;
  action?: string;
}

export const ActionStatus = ({ isRunning, envSuccess, action }: ActionStatusProps) => {
  if (isRunning) {
    return (
      <>
        <Spinner className="text-[#29CC83]" />
        <span className="text-[#29CC83]">Running</span>
      </>
    );
  }

  const isSuccess = envSuccess === undefined || envSuccess || action?.includes('file_editor str_replace');
  const color = isSuccess ? "#29CC83" : "#F3CA5F";
  const text = envSuccess === undefined ? "" : (isSuccess ? "Ran" : "Failed");

  return (
    <>
      <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none">
        {isSuccess ? (
          <path d="M20 6L9 17L4 12" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        ) : (
          <path d="M18 6L6 18M6 6L18 18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        )}
      </svg>
      <span className={`text-[${color}]`}>{text}</span>
    </>
  );
};