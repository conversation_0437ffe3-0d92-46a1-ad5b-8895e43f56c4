import { useState } from "react";
import { MessageCircleWarning, ChevronRight, ChevronDown } from "lucide-react";

interface ErrorDisplayProps {
  message: {
    error?: boolean;
    error_message?: string;
  };
}

export const ErrorDisplay = ({ message }: ErrorDisplayProps) => {
  const [isErrorExpanded, setIsErrorExpanded] = useState(false);

  if (!message.error || 
      (typeof message.error_message === 'string' && message.error_message.includes("context limit"))) {
    return null;
  }

  return (
    <div
      className="flex items-start gap-2 px-4 py-4 bg-[#CC5254]/10 border border-1 border-[#CC5254]/20 rounded-lg text-sm cursor-pointer"
      onClick={() => setIsErrorExpanded(!isErrorExpanded)}
    >
      <div className="flex flex-col items-start w-full gap-2">
        <div className="flex items-start justify-between w-full">
          <div className="flex items-start justify-center gap-2">
            <MessageCircleWarning className="w-5 h-5 text-[#E55C5C]" />
            <p className="text-[#E55C5C] font-['Inter'] text-base leading-6 font-normal">
              There has been an error
            </p>
          </div>
          {isErrorExpanded ? (
            <ChevronDown className="w-5 h-5 text-[#E55C5C]" />
          ) : (
            <ChevronRight className="w-5 h-5 text-[#E55C5C]" />
          )}
        </div>
        {isErrorExpanded && (
          <span className="text-[#C4C4CC]/70 font-['Inter'] text-base leading-6 font-normal">
            {JSON.stringify(message.error_message)}
          </span>
        )}
      </div>
    </div>
  );
};