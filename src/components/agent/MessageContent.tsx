import { formatMessageTimestamp } from "@/lib/utils/dateFormatter";
import RollbackTime from "@/assets/RollbackTime.svg";
import { calculateLinePositions, renderLine } from "../chat/TextRenderUtils";

interface MessageContentProps {
  message: {
    content: string;
    function_name?: string;
    timestamp: string;
    action?: string;
  };
  userName?: string;
  searchActive?: boolean;
  searchHighlights?: Array<{ startIndex: number; endIndex: number }>;
  handleRollback?: () => void;
}

export const MessageContent = ({ 
  message, 
  userName, 
  searchActive = false, 
  searchHighlights = [],
  handleRollback
}: MessageContentProps) => {
  
  // Handle special function types
  if (message.function_name === "default_tool") {
    return <span className="py-2 font-['Inter']">Hey {userName}, Quick input needed :</span>;
  }

  if (message.function_name === "ask_human" && !message.content) {
    return <span className="py-2 font-['Inter']">Hey {userName}, Quick input needed :</span>;
  }

  if (message.function_name === "ask_human" && message.action == "fork") {
    return <span className="py-2 font-['Inter']">Hey {userName}, Quick input needed :</span>;
  }

  if (message.function_name === "rollback") {
    return (
      <div className="relative flex flex-col items-center w-full space-y-4">
        <div className="flex w-full justify-between items-center gap-4 text-[15px] text-[#FFFFFF] font-['Inter'] py-1">
          <span className="font-['Inter']">
            Context has been cleared and a{" "}
            <span className="text-[#FF884D]">Code Checkpoint</span>{" "}
            has been created at{" "}
            <span className="text-[#FF884D]">
              {formatMessageTimestamp(message.timestamp)}
            </span>{" "}
          </span>

          <button
            onClick={handleRollback}
            className="bg-[#FF884D]/10 hover:bg-[#FF884D30] px-[10px] py-[6px] rounded-[8px] flex gap-1 items-center text-[#FF884DCC]"
          >
            <img
              alt="Time"
              src={RollbackTime}
              className="w-5 h-5"
            />
            Rollback
          </button>
        </div>
      </div>
    );
  }

  // Default content rendering
  const linePositions = calculateLinePositions(message.content);
  const contentLines = message.content.split("\n");

  return (
    <div className="flex flex-col prose prose-invert max-w-none">
      {contentLines.map((line, i) =>
        renderLine(
          line,
          i,
          false,
          searchActive,
          searchHighlights,
          linePositions[i]
        )
      )}
    </div>
  );
};