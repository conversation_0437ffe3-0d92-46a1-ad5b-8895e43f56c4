import Editor from "@monaco-editor/react";
import { DiffEditor } from "@monaco-editor/react";
import { getLanguageFromPath } from "@/lib/utils/codeFormatting";
import ReactMarkdown from "react-markdown";

interface ObservationRendererProps {
  message: {
    action?: string;
    observation?: string;
    function_name?: string;
  };
}

export const ObservationRenderer = ({ message }: ObservationRendererProps) => {
  
  const extractStrReplaceContent = (action: string) => {
    action = action.replace(/--status\b/, '');

    const parts = action.split("--old-str");
    if (parts.length < 2) return { oldStr: "", newStr: "" };

    const oldAndNew = parts[1].split("--new-str");
    if (oldAndNew.length < 2) return { oldStr: "", newStr: "" };

    const unescapeString = (str: string) => {
      return str
        .trim()
        .replace(/^["']|["']$/g, '')
        .replace(/\\n/g, "\n")
        .replace(/\\t/g, "\t")
        .replace(/\\r/g, "\r")
        .replace(/\\\\/g, "\\");
    };

    return {
      oldStr: unescapeString(oldAndNew[0]),
      newStr: unescapeString(oldAndNew[1]),
    };
  };

  const renderObservation = () => {
    if (!message.action) return message.observation;
    const [_, subCmd, filePath] = message.action.split(" ");

    if (subCmd === "view") {
      return (
        <div className="w-full h-[300px] border border-[#252526] rounded-lg overflow-hidden">
          <Editor
            height="100%"
            defaultLanguage={getLanguageFromPath(filePath)}
            defaultValue={
              message.observation?.includes("|")
                ? message.observation
                    ?.split("\n")
                    .slice(1)
                    .map((line) => {
                      const [, ...rest] = line.split("|");
                      return rest.join("|");
                    })
                    .join("\n")
                : message?.observation?.split("\n").slice(1).join("\n")
            }
            theme="vs-dark"
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              lineNumbers: "on",
              renderLineHighlight: "none",
              contextmenu: false,
              folding: true,
              scrollbar: {
                vertical: "visible",
                horizontal: "visible",
              },
            }}
          />
        </div>
      );
    } else if (subCmd === "str_replace") {
      const { oldStr, newStr } = extractStrReplaceContent(message.action);
      return (
        <div className="w-full h-[400px] border border-[#252526] rounded-lg overflow-hidden">
          <DiffEditor
            height="100%"
            language={getLanguageFromPath(filePath)}
            original={oldStr}
            modified={newStr}
            theme="vs-dark"
            options={{
              readOnly: true,
              renderSideBySide: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              lineNumbers: "on",
              renderLineHighlight: "none",
              contextmenu: false,
              folding: true,
              scrollbar: {
                vertical: "visible",
                horizontal: "visible",
              },
              originalEditable: false,
              enableSplitViewResizing: true,
            }}
          />
        </div>
      );
    } else if (subCmd === "create") {
      const fileContentMatch = message.action.match(/--file-text\s+(.*?)$/s);
      const fileContent = fileContentMatch
        ? fileContentMatch[1]
            .replace(/\\'/g, "'")
            .replace(/^'|'$/g, "")
            .replace(/\\n/g, "\n")
            .replace(/\\t/g, "\t")
            .replace(/\\r/g, "\r")
        : message.observation;

      return (
        <div className="w-full h-[400px] border border-[#252526] rounded-lg overflow-hidden">
          <Editor
            height="100%"
            defaultLanguage={getLanguageFromPath(filePath)}
            defaultValue={fileContent || ""}
            theme="vs-dark"
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              lineNumbers: "on",
              renderLineHighlight: "none",
              contextmenu: false,
              folding: true,
              scrollbar: {
                vertical: "visible",
                horizontal: "visible",
              },
            }}
          />
        </div>
      );
    } else if (message.action.startsWith("bulk_file_creator")) {
      return null;
    }

    return (
      <ReactMarkdown
        className="p-0 font-['Inter'] text-[16px]"
        components={{
          p: ({ node, ...props }) => <p className="my-2" {...props} />,
          h3: ({ node, ...props }) => (
            <h3 className="mt-4 mb-2 font-bold" {...props} />
          ),
          li: ({ node, ...props }) => <li className="my-1 ml-4" {...props} />,
          ul: ({ node, ...props }) => <ul className="my-2" {...props} />,
        }}
      >
        {message.observation}
      </ReactMarkdown>
    );
  };

  return (
    <div
      className="p-3 select-text selection:text-[#66EAFF] selection:bg-[#66EAFF] selection:bg-opacity-10"
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
      onDoubleClick={(e) => e.stopPropagation()}
    >
      {renderObservation()}
    </div>
  );
};