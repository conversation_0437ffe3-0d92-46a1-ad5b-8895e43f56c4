import { cn } from "@/lib/utils";
import AskQuestionSVG from "@/assets/mingcute_question-fill.svg";
import InfoIconSVG from "@/assets/info-square-02-contained-filled.svg";
import KeyboardArrowUp from "@/assets/keyboard_arrow_up.svg";
import { useState } from "react";
import StyledMarkdown from "../StyledMarkdown";
import { BorderBeam } from "../ui/border-beam";

interface SpecialMessageHandlersProps {
  message: {
    function_name?: string;
    action?: string;
    content: string;
  };
  nextMessageExists?: boolean;
}

export const SpecialMessageHandlers = ({ 
  message, 
  nextMessageExists = false 
}: SpecialMessageHandlersProps) => {
  const [showAskHuman, setShowAskHuman] = useState(false);

  const valueToRender = message.action == "fork" ? message.content : message.action;

  // Handle ask_human when next message doesn't exist (active question)
  if (message.function_name === "ask_human" && !nextMessageExists) {
    return (
      <div className={cn(
        "w-full p-[8px] relative rounded-[12px] bg-[#111112] border-[#FFFFFF1F] border-[1px] text-left flex flex-col gap-2"
      )}>
        <div className="flex items-center font-['Inter'] font-medium text-[#80FFF9] bg-[#80FFF91A] p-[10px] rounded-[8px] gap-2">
          <img
            src={AskQuestionSVG}
            alt="Ask Question"
            className="w-6 h-6 font-['Inter']"
          />
          <span className="font-['Inter']">
            Agent is asking a question, Please answer it to further continue :
          </span>
        </div>
        <div className="bg-[#FFFFFF0D] rounded-[8px] p-4 py-0">
          <StyledMarkdown
            children={valueToRender || ""}
            variant="default"
          />
        </div>
        <BorderBeam
          size={150}
          duration={12}
          delay={9}
          colorFrom="#80FFF9"
          colorTo="transparent"
        />
      </div>
    );
  }

  // Handle ask_human when next message exists (answered question)
  if (message.function_name === "ask_human") {
    return (
      <div className={cn(
        "w-full p-[8px] relative rounded-[12px] bg-[#111112] border-[#FFFFFF1F] border-[1px] text-left flex flex-col gap-2"
      )}>
        <div
          onClick={() => setShowAskHuman(!showAskHuman)}
          className="flex cursor-pointer items-center font-['Inter'] grayscale font-medium text-[#80FFF9] bg-[#80FFF91A] p-[10px] rounded-[8px] justify-between"
        >
          <div className="flex justify-between gap-2">
            <img
              src={AskQuestionSVG}
              alt="Ask Question"
              className="w-6 h-6 font-['Inter']"
            />
            <span className="font-['Inter']">
              Agent asked a question
            </span>
          </div>
          <div className="flex gap-2">
            <div className="bg-[#FFFFFF1A] rounded-full text-white px-[12px] py-1 text-[14px] font-medium">
              Answered
            </div>
            <button
              onClick={() => {
                setShowAskHuman(!showAskHuman);
              }}
            >
              <img
                src={KeyboardArrowUp}
                alt="Up"
                className={cn(
                  "transition-transform duration-200",
                  showAskHuman ? "rotate-180" : "rotate-0"
                )}
              />
            </button>
          </div>
        </div>
        {showAskHuman && (
          <div className="bg-[#FFFFFF0D] rounded-[8px] p-4 py-0">
            <StyledMarkdown
              children={valueToRender || ""}
              variant="default"
            />
          </div>
        )}
      </div>
    );
  }

  // Handle default_tool
  if (message.function_name === "default_tool") {
    return (
      <div className={cn(
        "w-full p-[8px] relative rounded-[12px] bg-[#111112] border-[#FFFFFF1F] border-[1px] text-left flex flex-col gap-2"
      )}>
        {!nextMessageExists && (
          <div className="flex items-center font-['Inter'] font-medium text-[#80FFF9] bg-[#80FFF91A] p-[10px] rounded-[8px] gap-2">
            <img
              src={InfoIconSVG}
              alt="Ask Question"
              className="w-6 h-6 font-['Inter']"
            />
            <span className="font-['Inter']">
              Agent will continue working after your reply
            </span>
          </div>
        )}
        <div className="bg-[#FFFFFF0D] rounded-[8px] p-4 py-0">
          <StyledMarkdown
            children={message.content || ""}
            variant="default"
          />
        </div>
        {!nextMessageExists ? (
          <BorderBeam
            size={150}
            duration={12}
            delay={9}
            colorFrom="#80FFF9"
            colorTo="transparent"
          />
        ) : null}
      </div>
    );
  }

  return null;
};