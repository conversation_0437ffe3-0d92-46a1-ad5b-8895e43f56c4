import AlertFillSVG from "@/assets/alert-fill.svg";
import AlertOrangeIcon from "@/assets/alert-orange.svg";
import { TokenDisplay } from "../TokenDisplay";
import ForkIcon from "@/assets/fork/fork.svg";
import Question from "@/assets/payments/question.svg"
import { URL_LINKS } from "@/constants/constants";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";

interface StatusIndicatorsProps {
  message: {
    action?: string;
    acc_cost?: number;
    max_budget?: number;
    current_token_count?: number;
    max_token_count?: number;
    error_message_exists?: boolean;
  };
  credits?: number;
  handleAddToken?: (variant: "increase_budget" | "add_credits") => void;
  podIsPaused?: boolean;
  modalOpen?: {
    fork: boolean;
  };
  setModalOpen?: (open: {
    fork: boolean;
  }) => void;
}

export const StatusIndicators = ({
  message,
  credits,
  handleAddToken,
  podIsPaused,
  modalOpen,
  setModalOpen,
}: StatusIndicatorsProps) => {

  const isEmergentUser = useIsEmergentUser();

  const getTokenUsagePercentage = (
    currentTokenCount: number | undefined,
    maxTokenCount: number | undefined,
    errorMessageExists: any
  ): number => {
    if (!currentTokenCount || !maxTokenCount || maxTokenCount === 0) {
      return 0;
    }
    return Math.min(
      errorMessageExists ? 100 : 99,
      Math.round((currentTokenCount / maxTokenCount) * 100)
    );
  };

  const getTokenUsageColor = (errorMessageExists: any): string => {
    return !errorMessageExists ? "text-[#F2994A]" : "text-[#E55C5C]";
  };

  const formatTokenCount = (
    tokenCount: number | undefined,
    maxTokenCount?: number | undefined
  ): number => {
    if (!tokenCount) {
      return 0;
    }
    const safeTokenCount = maxTokenCount
      ? Math.min(tokenCount, maxTokenCount)
      : tokenCount;
    return Math.round(safeTokenCount / 1000);
  };

  // Credit exhausted
  if (message.action === "exit_cost_credit_limit_reached") {
    return (
      <div className="flex items-center justify-between gap-2 p-3 bg-[#1b1b1b] rounded-lg text-sm">
        <div className="flex items-center justify-center gap-2">
          <img
            src={AlertFillSVG}
            alt="Alert"
            className="w-5 h-5 text-[#E55C5C]"
          />
          <p className="text-[#E6E6E6] font-['Inter'] leading-6 font-normal">
            Add More Credits
          </p>
        </div>
      </div>
    );
  }

  // Budget exhausted
  if (message.action === "exit_cost") {
    return (
      <div className="flex items-center justify-between gap-2 p-3 bg-[#1b1b1b] rounded-lg text-sm">
        <div className="flex items-center justify-center gap-2">
          <img
            src={AlertFillSVG}
            alt="Alert"
            className="w-5 h-5 text-[#E55C5C]"
          />
          <p className="text-[#E6E6E6] font-['Inter'] leading-6 font-normal">
            Budget Exhausted
          </p>
        </div>
        <TokenDisplay
          acc_cost={message.acc_cost}
          max_budget={message.max_budget}
          onAddToken={() => {
            handleAddToken && !podIsPaused && handleAddToken("increase_budget");
          }}
        />
      </div>
    );
  }

  // Context limit reached
  if (message.action === "context_limit_reached" && isEmergentUser) {
    const percentage = getTokenUsagePercentage(
      message.current_token_count,
      message.max_token_count,
      message.error_message_exists
    );

    return (
      <div className="bg-[#1111112] border-[#FFFFFF1F] border p-2 rounded-[16px] flex flex-col gap-2">
        <div className="flex items-center justify-between gap-2 p-[10px] rounded-[8px] bg-[#FFAE6614]">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <img
                src={
                  !message.error_message_exists ? AlertOrangeIcon : AlertFillSVG
                }
                alt="Alert"
                className={`w-5 h-5 ${getTokenUsageColor(
                  message.error_message_exists
                )}`}
              />
              <span
                className={getTokenUsageColor(message.error_message_exists)}
              >
                {percentage}%
              </span>
            </div>
            <span className="font-medium">Agent context used</span>
          </div>
          <span className="font-medium text-white">
            <span className={getTokenUsageColor(message.error_message_exists)}>
              {formatTokenCount(
                message.current_token_count,
                message.max_token_count
              )}
              K{" "}
            </span>
            / {formatTokenCount(message.max_token_count)}K tokens
          </span>
        </div>
        <div className="flex items-center justify-between gap-2 text-sm bg-[#FFFFFF0D] rounded-lg">
          <div className="flex flex-col items-start gap-6 p-4 py-5">
            {/* <p className="text-white font-['Inter'] leading-6 font-medium text-opacity-60">
              {!message.error_message_exists
                ? "You're just 1-2 messages away from hitting the limit. Once full, Our agent will pause all interactions."
                : "You have reached the context limit for this chat!"}
            </p> */}

            <div className="flex flex-col gap-3">
              <span className="font-medium text-white text-[24px] font-brockmann">
              Summarize & continue in a fresh session
            </span>

            <span className="text-[#737780] text-[15px] leading-[24px] font-[500] text-start font-['Inter']">Continue your work in a new conversation while keeping the important context from this session, perfect for continuing complex projects without losing progress.</span>

            </div>
            {/* <div className="grid grid-cols-3 gap-4"> */}
              {/* Context preserved */}

              {/* {INFO.map((item, index) => (
                <div
                  key={index}
                  className="flex flex-col rounded-[8px] p-4 items-start w-full text-center bg-[#FFFFFF0A] gap-[24px]"
                >
                  <img src={item.icon} alt={item.title} className="w-5 h-5 grayscale" />
                  <div className="flex flex-col items-start w-full gap-[6px]">
                    <h3 className="text-[16px] text-[#FFFFFF] font-medium">
                      {item.title}
                    </h3>
                    <p className="text-[14px] font-[500] text-start text-[#FFFFFF66] font-['Inter']">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))} */}
            {/* </div> */}
            
            <div className="flex  justify-between w-full border-[#242424]">
              <button
                onClick={() => {
                  modalOpen && setModalOpen && setModalOpen({
                    ...modalOpen,
                    fork: !modalOpen.fork,
                  });
                }}
                className="pl-4 pr-3 py-[10px] h-[40px] bg-white text-black hover:bg-gray-100 rounded-[10px] font-[600] text-[16px] flex items-center gap-2"
              >
                Fork Session
                <img src={ForkIcon} alt="Fork" className="w-5 h-5 invert" />

              </button>
              <button
                onClick={() => {
                  window.open(
                      URL_LINKS.forking.learnMore,
                    "_blank"
                  );
                }}
                className="pl-4 pr-3 py-[10px] h-[40px] bg-transparent text-white/70 hover:bg-[#FFFFFF0A] rounded-[10px] font-[500] text-[16px] flex items-center gap-2"
              >
                Learn more about Forking
                <img src={Question} alt="Question" className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }



  // Context limit reached
  if (message.action === "context_limit_reached" && !isEmergentUser) {
    const percentage = getTokenUsagePercentage(
      message.current_token_count,
      message.max_token_count,
      message.error_message_exists
    );

    return (
      <div className="bg-[#1E1E1F] p-3 rounded-lg">
        <div className="flex items-center justify-between gap-2 pb-3">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <img
                src={!message.error_message_exists ? AlertOrangeIcon : AlertFillSVG}
                alt="Alert"
                className={`w-5 h-5 ${getTokenUsageColor(message.error_message_exists)}`}
              />
              <span className={getTokenUsageColor(message.error_message_exists)}>{percentage}%</span>
            </div>
            <span className="font-medium">Agent context used</span>
          </div>
          <span className="font-medium text-white">
            <span className={getTokenUsageColor(message.error_message_exists)}>
              {formatTokenCount(message.current_token_count, message.max_token_count)}K
            </span>
            / {formatTokenCount(message.max_token_count)}K
          </span>
        </div>
        <div className="flex items-center justify-between gap-2 p-3 text-sm bg-white rounded-lg bg-opacity-5">
          <div className="flex items-center justify-center gap-2">
            <p className="text-white font-['Inter'] leading-6 font-medium text-opacity-60">
              {!message.error_message_exists
                ? "You're just 1-2 messages away from hitting the limit. Once full, Our agent will pause all interactions."
                : "You have reached the context limit for this chat!"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return null;
};
