import { formatAgentTimestamp } from "@/lib/utils/dateFormatter";

interface StatusSeparatorProps {
  message: {
    action?: string;
    acc_cost?: number;
    max_budget?: number;
    timestamp: string;
  };
  credits?: number;
  max_budget?: number;
  user?: any;
  session?: any;
}

// This renders BEFORE the message content
export const TopStatusSeparators = ({ 
  message, 
  credits, 
  max_budget, 
  user,
  session 
}: StatusSeparatorProps) => {
  
  // Budget Exhausted separator (appears before message)
  if (user && session && message.acc_cost && message.max_budget && 
      message.action === "exit_cost" && 
      message.acc_cost > message.max_budget && 
      message.acc_cost > (max_budget || 0)) {
    return (
      <div className="flex flex-col w-full pt-4 space-y-4">
        <div className="flex w-full gap-4">
          <div className="w-[16px] md:w-[120px] h-[1px] bg-[#4D401F] mt-[10px]"></div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col items-start">
              <div className="flex items-center w-full gap-4">
                <span className="text-[#F5CC62] font-berkeley text-[13px] md:text-[15px] leading-[20px] font-normal">
                  Budget Exhausted
                </span>
                <div className="h-[1px] flex-1 bg-[#4D401F]"></div>
              </div>
              <span className="text-[#5C5F66] font-berkeley mt-2 text-[13px] md:text-[15px] leading-[20px] font-normal text-right">
                {formatAgentTimestamp(message.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Credit Exhausted separator (appears before message)
  if (user && session && credits !== undefined && 
      message.action === "exit_cost_credit_limit_reached" && 
      credits < 0) {
    return (
      <div className="flex flex-col w-full pt-4 space-y-4">
        <div className="flex w-full gap-4">
          <div className="w-[16px] md:w-[120px] h-[1px] bg-[#4D401F] mt-[10px]"></div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col items-start">
              <div className="flex items-center w-full gap-4">
                <span className="text-[#F5CC62] font-berkeley text-[13px] md:text-[15px] leading-[20px] font-normal">
                  Credit Exhausted
                </span>
                <div className="h-[1px] flex-1 bg-[#4D401F]"></div>
              </div>
              <span className="text-[#5C5F66] font-berkeley mt-2 text-[13px] md:text-[15px] leading-[20px] font-normal text-right">
                {formatAgentTimestamp(message.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// This renders AFTER the message content
export const BottomStatusSeparators = ({ 
  message, 
  credits, 
  max_budget 
}: StatusSeparatorProps) => {
  
  // Budget Increased separator (appears after message)
  if (message.action === "exit_cost" && 
      message.acc_cost !== undefined && 
      max_budget !== undefined && 
      message.acc_cost < max_budget) {
    return (
      <div className="flex flex-col w-full pt-4 space-y-4">
        <div className="flex w-full gap-4">
          <div className="w-[16px] md:w-[120px] h-[1px] bg-[#4D401F] mt-[10px]"></div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col items-start">
              <div className="flex items-center w-full gap-4">
                <span className="text-[#F5CC62] font-berkeley text-[13px] md:text-[15px] leading-[20px] font-normal">
                  Budget Increased
                </span>
                <div className="h-[1px] flex-1 bg-[#4D401F]"></div>
              </div>
              <span className="text-[#5C5F66] font-berkeley mt-2 text-[13px] md:text-[15px] leading-[20px] font-normal text-right">
                {formatAgentTimestamp(message.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Credits Recharged separator (appears after message)
  if (message.action === "exit_cost_credit_limit_reached" && 
      credits !== undefined && 
      message.max_budget !== undefined && 
      credits > 0) {
    return (
      <div className="flex flex-col w-full pt-4 space-y-4">
        <div className="flex w-full gap-4">
          <div className="w-[16px] md:w-[120px] h-[1px] bg-[#4D401F] mt-[10px]"></div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col items-start">
              <div className="flex items-center w-full gap-4">
                <span className="text-[#F5CC62] font-berkeley text-[13px] md:text-[15px] leading-[20px] font-normal">
                  Credits Recharged
                </span>
                <div className="h-[1px] flex-1 bg-[#4D401F]"></div>
              </div>
              <span className="text-[#5C5F66] font-berkeley mt-2 text-[13px] md:text-[15px] leading-[20px] font-normal text-right">
                {formatAgentTimestamp(message.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Agent Finished separator (appears after message)
  if (message.action === "finish") {
    return (
      <div className="flex flex-col w-full pt-4 space-y-4">
        <div className="flex w-full gap-4">
          <div className="w-[16px] md:w-[120px] h-[1px] bg-[#213B2E] mt-[10px]"></div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col items-start">
              <div className="flex items-center w-full gap-4">
                <span className="text-[#00E573] font-berkeley text-[15px] leading-[20px] font-normal">
                  Agent Finished
                </span>
                <div className="h-[1px] flex-1 bg-[#213B2E]"></div>
              </div>
              <span className="text-[#5C5F66] font-berkeley mt-2 text-[13px] md:text-[15px] leading-[20px] font-normal text-right">
                {formatAgentTimestamp(message.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};