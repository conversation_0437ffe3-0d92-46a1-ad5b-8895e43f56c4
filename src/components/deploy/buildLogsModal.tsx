import { cn } from "@/lib/utils";
import CopyIconSVG from "@/assets/copy_white.svg";
import ArticleSVG from "@/assets/deployment/Article.svg";
import Dangerous from "@/assets/deployment/dangerous.svg";
import DangerousActive from "@/assets/deployment/dangerous_active.svg";
import WarningInactive from "@/assets/deployment/warning_inactive.svg"
import WarningActive from "@/assets/deployment/warning_active.svg"
import { useState } from "react";
import { X, Check } from "lucide-react";

interface BuildLogsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShareLogs: ( logs: string[]) => void;
  deployLogs?: string[];
}

const lineVariants = {
  info: "text-[#EDEDED] bg-[#EDEDED00] hover:bg-[#EDEDED26]",
  warning: "text-[#E38F45] bg-[#E38F4526] hover:bg-[#E38F454D]",
  error: "text-[#FF6666] bg-[#FF666626] hover:bg-[#FF66664D]",
};

interface LogsLineProps {
  timestamp: string;
  type: "info" | "warning" | "error";
  message: string;
}

const LogsLineData: LogsLineProps[] = [];

interface LogCategoryProps {
  name: string;
  icon: string;
  activeIcon: string;
  type: "all" | "warning" | "error";
}

const LogCategories: LogCategoryProps[] = [
  {
    name: "All Logs",
    icon: "",
    activeIcon: "",
    type: "all",
  },
  {
    name: "Error Logs",
    icon: Dangerous,
    activeIcon: DangerousActive,
    type: "error",
  },
  {
    name: "Warning Logs",
    icon:   WarningInactive,
    activeIcon: WarningActive,
    type: "warning",
  },
];

function BuildLogsModal({ isOpen, onClose, onShareLogs, deployLogs = [] }: BuildLogsModalProps) {
  const [activeLogCategory, setActiveLogCategory] = useState<LogCategoryProps>(
    LogCategories[0]
  );
  const [copyFeedback, setCopyFeedback] = useState(false);

  // Convert deployLogs array to LogsLineProps format
  const processedLogs: LogsLineProps[] = deployLogs.map((log) => {
    // Determine log type based on content
    let type: "info" | "warning" | "error" = "info";

    // Enhanced heuristic to determine log type based on content
    const logLower = log.toLowerCase();
    if (logLower.includes('error') ||
        logLower.includes('failed') ||
        logLower.includes('exception') ||
        logLower.includes('fatal') ||
        logLower.includes('critical')) {
      type = "error";
    } else if (logLower.includes('warn') ||
               logLower.includes('warning') ||
               logLower.includes('deprecated') ||
               logLower.includes('skipping')) {
      type = "warning";
    }

    return {
      timestamp: "", // Not needed since message contains timestamp
      type,
      message: log
    };
  });

  // Use processed logs if available, otherwise fall back to dummy data
  const logsToDisplay = processedLogs.length > 0 ? processedLogs : LogsLineData;

  if (!isOpen) return null;

  return (
    <div
      className={cn(
        "absolute inset-0 z-[49] top-0 bottom-0 bg-black/50 backdrop-blur-[10px] flex pt-[69px] w-full"
      )}
    >
      <div className="flex flex-col flex-1 w-full">
        <div className="flex h-[36px] w-full justify-between border border-t-0 border-[#242424]">
          <div className="flex h-[36px]">
            {LogCategories.map((logType) => {
              // Calculate count for each log type
              const count = logType.type === "all"
                ? logsToDisplay.length
                : logsToDisplay.filter(log => log.type === logType.type).length;

              return (
                <div
                  key={logType.name}
                  onClick={() => setActiveLogCategory(logType)}
                  className={cn(
                    "h-[36px] px-6 py-2 flex items-center justify-center cursor-pointer gap-1  border-[#242424] border-t-0 border-b-0 border-r",
                    activeLogCategory.name === logType.name ?
                      "bg-[#FFFFFF] text-[#131314]" :""
                  )}
                >
                  {logType.icon && <img
                    src={
                      activeLogCategory === logType
                        ? logType.activeIcon
                        : logType.icon
                    }
                    alt={logType.name}
                    className="w-5 h-5 max-md:hidden"
                  />}
                  <span className={cn("text-[#131314] font-['Inter'] max-md:text-[10px] text-[13px] font-semibold", activeLogCategory.name === logType.name ? "text-[#131314]" : "text-[#FFFFFF50]")}>
                    {logType.name} <span className="font-['Inter'] max-md:hidden">({count})</span>
                  </span>
                </div>
              );
            })}
          </div>
          <div
            onClick={onClose}
            className="bg-[#242424] h-full px-6 py-2 gap-1 flex items-center justify-center cursor-pointer"
          >
            {/* <div className="flex items-center justify-center w-5 h-5"> */}
                <X className="w-5 h-5 p-[1px]" />
            {/* </div> */}
            <span className="text-[#FFFFFF] font-['Inter'] text-[13px] font-semibold ">
              Close
            </span>
          </div>
        </div>
        <div className="flex-1 w-full h-full pt-2 overflow-x-scroll overflow-y-scroll md:pt-5 hide-scrollbars">
          {logsToDisplay.length === 0 ? (
            <div className="flex items-center justify-center h-full text-[#FFFFFF] text-center">
              <div>
                <div className="mb-2 text-lg">No logs available</div>
                <div className="text-sm text-[#06050550]">Logs will appear here when the build process starts</div>
              </div>
            </div>
          ) : (
            <div className="min-w-max">
              {logsToDisplay.filter(
                (log) =>
                  activeLogCategory.type === "all" ||
                  log.type === activeLogCategory.type
              ).map((log, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex h-[36px] w-full px-6 py-2 items-center min-w-max",
                    lineVariants[log.type as keyof typeof lineVariants]
                  )}
                >
                  <span
                    className="font-berkeley text-[12px]  md:text-[14px] whitespace-nowrap"
                  >
                    {log.message}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
        {deployLogs.length>0 && <div className=" bg-[#09090A] p-6 flex justify-end gap-4 border-t border-[#242424]">
          <button
            type="button"
            title="Copy"
            disabled={logsToDisplay.length === 0}
            onClick={async () => {
              if (logsToDisplay.length > 0) {
                try {
                  const logsToCopy = logsToDisplay
                    .filter(
                      (log) =>
                        activeLogCategory.type === "all" ||
                        log.type === activeLogCategory.type
                    )
                    .map((log) => log.message)
                    .join('\n');

                  await navigator.clipboard.writeText(logsToCopy);

                  // Show feedback
                  setCopyFeedback(true);
                  setTimeout(() => setCopyFeedback(false), 2000);
                } catch (err) {
                  console.error('Failed to copy logs:', err);
                }
              }
            }}
            className={cn(
              "p-2 md:p-3 md:pr-4 md:pl-5 max-md:text-[12px] rounded-full flex items-center gap-2 tracking-[-0.2px] font-semibold transition-colors",
              logsToDisplay.length === 0
                ? "bg-[#E6E6E610] text-[#E6E6E650] cursor-not-allowed"
                :  "bg-[#E6E6E61A] text-[#E6E6E6] hover:bg-[#E6E6E620] cursor-pointer"
            )}
          >
            {copyFeedback ? (
              <>
                Copied! <Check className="w-6 h-6" />
              </>
            ) : (
              <>
                Copy <img src={CopyIconSVG} alt="Copy" className={cn("max-md:w-4 max-md:h-4 w-6 h-6", logsToDisplay.length === 0 ? "opacity-50" : "")} />
              </>
            )}
          </button>
          <button
            type="button"
            title="Share Logs with Agent"
            disabled={logsToDisplay.length === 0}
            onClick={()=>{
              if (logsToDisplay.length > 0) {
                onShareLogs(logsToDisplay.filter(
                  (log) =>
                    activeLogCategory.type === "all" ||
                    log.type === activeLogCategory.type
                ).map((log) => log.message));
              }
            }}
            className={cn(
              "p-2 md:p-3 md:pr-4 md:pl-5 max-md:text-[12px] rounded-full flex items-center font-semibold gap-2 tracking-[-0.2px]",
              logsToDisplay.length === 0
                ? "bg-[#E6E6E650] text-[#0E0E0F50] cursor-not-allowed"
                : "bg-[#E6E6E6] text-[#0E0E0F] hover:bg-[#E6E6E690] cursor-pointer"
            )}
          >
            Share Logs with Agent{" "}
            <img src={ArticleSVG} alt="Copy" className={cn("max-md:w-4 max-md:h-4 w-6 h-6", logsToDisplay.length === 0 ? "opacity-50" : "")} />
          </button>
        </div>}
      </div>
    </div>
  );
}

export default BuildLogsModal;
