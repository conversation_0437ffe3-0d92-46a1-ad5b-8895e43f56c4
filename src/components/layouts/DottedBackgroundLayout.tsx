import React from "react";
import DottedBackground from "../ui/dotted-background";
import { DotPattern } from "../ui/dot-pattern";
import { cn } from "@/lib/utils";

interface DottedLayoutProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  dotConfig?: {
    size?: number;
    spacing?: number;
    maxDistance?: number;
    repulsionForce?: number;
    returnSpeed?: number;
    defaultColor?: string;
    activeColor?: string;
    defaultOpacity?: number;
  };
}

const DottedLayout = ({
  children,
  className = "",
  containerClassName = "",
  dotConfig,
}: DottedLayoutProps) => {
  return (
      <div className={`relative min-h-screen w-full ${className}`}>
      <div className={`relative min-h-screen z-10 ${containerClassName}`}>
        {children}
      </div>
    </div>
    
  );
};

export default DottedLayout;
