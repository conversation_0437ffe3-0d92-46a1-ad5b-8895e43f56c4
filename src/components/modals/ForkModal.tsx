import React from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import ForkIcon from "@/assets/fork/fork.svg";
import DollarCyanSVG from "@/assets/fork/dollar_cyan.svg"
import Clock<PERSON>yanSVG from "@/assets/fork/pace_cyan.svg"
import LikeCyanSVG from "@/assets/fork/like_cyan.svg"


interface ForkModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onFork?: () => void;
}

interface InfoInterface {
  title: string;
  description: string;
  icon: string;
}

export const INFO: InfoInterface[] = [
  {
    title: "Context preserved",
    description: "All important context preserved in summary",
    icon: LikeCyanSVG,
  },
 
  {
    title: "Memory refreshed",
    description: "Fresh context window with clean slate for new tasks",
    icon: ClockCyanSVG,
  },
   {
    title: "Spend less",
    description: "Refreshed memory means lower token costs",
    icon: DollarCyanSVG,
  },
];

export const ForkModal: React.FC<ForkModalProps> = ({
  isOpen,
  onOpenChange,
  onFork,
}) => {
  const handleFork = () => {
    onFork?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-md:w-[95vw] md:max-w-[740px] p-0 bg-[#18181A] border border-[#242424] rounded-[16px] font-['Inter']">
        <div className="flex flex-col gap-4 p-4 md:gap-6 md:p-8">
          <div className="flex justify-between md:gap-[10px] flex-col">
            <div className="flex items-center justify-between md:gap-[10px]">
              <DialogTitle className="text-[18px] md:text-[22px] font-medium text-white">
                Fork this session with a summary
              </DialogTitle>
              <button
                type="button"
                onClick={() => onOpenChange(false)}
                className="w-6 h-6 flex items-center justify-center text-[#737780] hover:text-white transition-colors"
                aria-label="Close modal"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <p className="text-[13px] md:text-[16px] font-[400] md:leading-[24px] text-[#8A8F98] font-['Inter']">
              Continue your work in a new conversation while keeping the
              important context from this session, perfect for continuing
              complex projects without losing progress.
            </p>
          </div>

          <div className="grid gap-2 md:gap-4 md:grid-cols-3">
            {/* Context preserved */}

            {INFO.map((item, index) => (
              <div
                key={index}
                className="flex md:flex-col rounded-[8px] p-4 py-6 items-start w-full text-center bg-[#80FFF90A] gap-2 md:gap-[3rem]"
              >
                <img src={item.icon} alt={item.title} className="w-5 h-5" />
                <div className="flex flex-col items-start w-full gap-[6px]">
                  <h3 className="text-[14px] md:text-[16px] text-[#80FFF9] font-medium">
                    {item.title}
                  </h3>
                  <p className="text-[12px] md:text-[14px] font-[500] text-start text-[#80FFF966] font-['Inter']">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-3 md:p-6 border-t border-[#242424]">
          <button
            type="button" 
            onClick={() => onOpenChange(false)}
            className="px-6 py-2 h-[40px] md:h-[48px] bg-transparent border border-[#333333] text-[#DCDCE5] hover:bg-[#FFFFFF0A] hover:border-[#444444] rounded-full font-[500] text-[16px]"
          >
            Cancel
          </button>
          <button
            onClick={handleFork}
            className="px-6 py-2 h-[40px] md:h-[48px] bg-white text-black hover:bg-gray-100 rounded-full font-[600] text-[16px] flex items-center gap-2"
          >
            <img src={ForkIcon} alt="Fork" className="w-6 h-6 invert" />
            Fork
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
