import { useState, useEffect } from 'react';
import { X, Loader2, ChevronDown, Plus, RefreshCw, Search, AlertTriangle, Check } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogFooter,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
// Select components removed as we're using custom dropdowns
import { useGitHubPush, GitHubPushParams } from '@/hooks/useGitHubPush';
import { agentApi } from '@/services/agentApi';
import ipc from '@/lib/ipc/client';
import { isElectron as isElectronEnv } from '@/lib/utils/isElectron';
import { useToast } from '@/hooks/use-toast';
import GithubIcon from '@/assets/github.svg';
import GithubIconFull from "@/assets/menu-icons/github.svg"
// ConnectedGitHubIcon removed as it's not used
import RepositoryIcon from '@/assets/repo.svg';
import BranchIcon from '@/assets/branch.svg';
import InfoSquareIcon from '@/assets/info-square.svg';
import WarningPurple from '@/assets/warning-purple.svg';
import SaveCloudIcon from '@/assets/save-cloud.svg';

import { useGitHub } from '@/contexts/GitHubContext';
import { Badge } from '../ui/badge';
// PulseDot removed as it's not used

import ConnectWithGithub from "@/assets/github_connect.svg"
import { cn } from '@/lib/utils';
import { isValidGitName as validateGitName, isValidRepoName as validateRepoName, replaceSpacesWithHyphens } from '@/lib/utils/gitValidation';
import LineSVG from "@/assets/Line.svg"

interface GitHubPushModalProps {
  podIsPaused?: boolean;
  isOpen: boolean;
  lastGithubUsed?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  } | null;
  onOpenChange: (open: boolean) => void;
  jobId?: string;
  onSuccess?: (repoDetails?: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  }) => void;
}

interface Branch {
  name: string;
  commit?: {
    sha: string;
    url: string;
  };
}

export function GitHubPushModal({
  podIsPaused,
  isOpen,
  onOpenChange,
  jobId,
  lastGithubUsed,
  onSuccess
}: GitHubPushModalProps) {
  const [branchName, setBranchName] = useState('main');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [needsForcePush, setNeedsForcePush] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(false);
  const [showBranchDropdown, setShowBranchDropdown] = useState(false);
  const [showCustomBranchInput, setShowCustomBranchInput] = useState(false);
  const [customBranchName, setCustomBranchName] = useState('');
  // filteredBranches removed as we're using a different approach for branch selection
  const [showRepositoryDropdown, setShowRepositoryDropdown] = useState(false);
  const [showInstallationDropdown, setShowInstallationDropdown] = useState(false);
  const [repoNotFound, setRepoNotFound] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true); // Add state for initial loading
  const {
    isPushing,
    error,
    success,
    installations,
    repositories,
    selectedInstallation,
    selectedRepository,
    isLoadingInstallations,
    isLoadingRepositories,
    isCreatingNewRepo,
    newRepoName,
    fetchInstallations,
    fetchRepositories,
    setSelectedInstallation,
    setSelectedRepository,
    setIsCreatingNewRepo,
    setNewRepoName,
    pushToGitHub,
    reset
  } = useGitHubPush();
  const { toast } = useToast();
  const {
    redirectToGitHubInstallation,
    isConnected,
    setIsConnected,
    isConnecting,
    setIsConnecting
  } = useGitHub();
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    setIsElectron(isElectronEnv());
  }, []);

  // Reset form when modal closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      reset();
      setNeedsForcePush(false);
      setBranchName('main');
      setBranches([]);
      // setFilteredBranches removed
      setShowBranchDropdown(false);
      setShowCustomBranchInput(false);
      setCustomBranchName('');
      setRepoNotFound(false);
      setInitialLoading(true); // Reset to true for next open
    }
    onOpenChange(open);
  };

  // Check if error indicates need for force push
  useEffect(() => {
    if (error && error.includes('Conflict Detected')) {
      setNeedsForcePush(true);
    } else {
      setNeedsForcePush(false);
    }
  }, [error]);

  // Fetch installations and check GitHub connection when modal opens
  useEffect(() => {
    if (!isOpen) return;

    // Set initial loading state to true
    setInitialLoading(true);

    // Set a timeout to prevent infinite loading (5 seconds max)
    const loadingTimeout = setTimeout(() => {
      setInitialLoading(false);
      //console.log("Loading timeout reached - forcing loading state to complete");
    }, 5000);

    // Directly check user details from API
    const checkUserDetails = async () => {
      try {
        //console.log("Checking user details on modal open...");
        // Get user details directly from the API
        const userDetails = await agentApi.getUserDetails();
        //console.log("User details response on modal open:", userDetails);

        // Check if GitHub is authorized in the response
        const isAuthorized = userDetails && userDetails.github && userDetails.github.authorized === true;
        //console.log("GitHub authorization status:", isAuthorized);

        // Update the connection state based on the API response
        setIsConnected(isAuthorized);

        if (isAuthorized) {
          // If authorized, fetch installations
          try {
            await fetchInstallations();
          } catch (err) {
            console.error("Error fetching installations:", err);
            // Show error in toast instead of getting stuck in loading
            toast({
              title: "Error",
              description: "Failed to load GitHub installations. Please try again.",
              variant: "destructive"
            });
          }
        }
      } catch (error) {
        console.error("Error checking user details on modal open:", error);
        // Show error in toast
        toast({
          title: "Error",
          description: "Failed to check GitHub connection. Please try again.",
          variant: "destructive"
        });
      } finally {
        // Clear the timeout and set loading to false
        clearTimeout(loadingTimeout);
        setTimeout(() => {
          setInitialLoading(false);
        }, 500);
      }
    };

    // Execute the check
    checkUserDetails();

    setNeedsForcePush(false);

    // Always set a default branch name
    setBranchName('main');

    // Cleanup function to clear timeout if component unmounts
    return () => {
      clearTimeout(loadingTimeout);
    };
  }, [isOpen]);

  // Monitor installation changes to reset repository-related state
  useEffect(() => {
    if (selectedInstallation) {
      // Reset repository-related state when installation changes
      setSelectedRepository('');
      setSearchQuery('');
      setBranches([]);
      setIsCreatingNewRepo(false);
      setNewRepoName('');
      setRepoNotFound(false);

      // Reset branch name to default
      setBranchName('main');

      // Force the input field to clear by setting an empty value
      setTimeout(() => {
        const repoInput = document.querySelector('.repository-input input') as HTMLInputElement;
        if (repoInput) {
          repoInput.value = '';
        }
      }, 0);
    }
  }, [selectedInstallation]);

  // Reset connecting state if we're connected but still showing connecting UI
  useEffect(() => {
    if (isConnected && isConnecting) {
      //console.log("Connection successful but still showing connecting UI, resetting state");
      setIsConnecting(false);

      // Force reload the modal to show the connected state
      handleOpenChange(false);
      setTimeout(() => {
        handleOpenChange(true);
      }, 100);
    }
  }, [isConnected, isConnecting, handleOpenChange]);

  // Handle lastGithubUsed when repositories are loaded
  useEffect(() => {
    // Only proceed if we have lastGithubUsed data, repositories are loaded, and we're not already creating a new repo
    if (lastGithubUsed?.repo && repositories.length > 0 && !isCreatingNewRepo && !isLoadingRepositories) {
      //console.log('Looking for last used repo:', lastGithubUsed);

      // Find the repository that matches the lastGithubUsed owner and repo
      const matchingRepo = repositories.find(repo =>
        repo.name === lastGithubUsed.repo &&
        repo.full_name.startsWith(`${lastGithubUsed.owner}/`)
      );

      if (matchingRepo) {
        //console.log('Found matching repo:', matchingRepo);
        // Set the selected repository and search query
        setSelectedRepository(matchingRepo.name);
        setSearchQuery(matchingRepo.name);

        // Also fetch branches for this repository
        fetchBranches(matchingRepo.name);
      } else {
        //console.log('No matching repo found for:', lastGithubUsed);
      }
    }
  }, [repositories, isLoadingRepositories]);

  // Fetch branches when repository is selected
  useEffect(() => {
    if (selectedRepository && !isCreatingNewRepo) {
      fetchBranches(selectedRepository);
    } else {
      setBranches([]);
    }
  }, [selectedRepository, isCreatingNewRepo]);



  // Get account login from selected installation
  const getAccountLogin = () => {
    const installation = installations.find(i => i.installation_id === selectedInstallation);
    return installation?.account_login || '';
  };

  // Get repo name from selected repository
  const getRepoName = () => {
    if (isCreatingNewRepo) {
      return newRepoName;
    }
    // Find repository by name instead of full_name
    const repo = repositories.find(r => r.name === selectedRepository);
    return repo?.name || '';
  };

  // Fetch branches for a repository
  const fetchBranches = async (repoName: string) => {
    if (!repoName) return;

    setIsLoadingBranches(true);
    setBranches([]);

    try {
      // Get account login from selected installation
      const installation = installations.find(i => i.installation_id === selectedInstallation);
      const account_login = installation?.account_login || '';

      if (!account_login) {
        throw new Error('Invalid account login');
      }

      const response = await agentApi.getGitHubBranches(account_login, repoName);

      if (response.error) {
        throw new Error(response.error);
      }

      // Handle direct array response or response with branches property
      const branchList = Array.isArray(response) ? response :
                        (response.branches || []);

      setBranches(branchList);

      // Auto-select default branch (usually 'main' or 'master') if available
      const defaultBranch = branchList.find((b: Branch) => b.name === 'main' || b.name === 'master');
      if (defaultBranch) {
        setBranchName(defaultBranch.name);
      } else if (branchList.length > 0) {
        setBranchName(branchList[0].name);
      } else {
        setBranchName('main'); // Default fallback
      }
    } catch (err) {
      console.error('Error fetching branches:', err);
    } finally {
      setIsLoadingBranches(false);
    }
  };

  // Handle refresh repositories
  const handleRefreshRepositories = async () => {
    if (!selectedInstallation || isLoadingRepositories || isRefreshing) return;

    setIsRefreshing(true);
    await fetchRepositories(selectedInstallation);
    setIsRefreshing(false);
  };

  // Handle refresh branches
  const handleRefreshBranches = async () => {
    if (!selectedRepository || isLoadingBranches || isCreatingNewRepo) return;

    await fetchBranches(selectedRepository);
  };

  // Filter repositories based on search query
  const filteredRepositories = repositories.filter(repo => {
    if (!searchQuery) return true;
    return repo.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Check if branch name exists
  const branchExists = branches.some(branch => branch.name === branchName);



  // Handle branch input change
  const handleBranchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const oldValue = e.target.value;
    // Only replace spaces with hyphens, but validate other characters
    const value = replaceSpacesWithHyphens(oldValue);
    setBranchName(value);

    // If we're in the custom branch input mode, just update the branch name
    if (showCustomBranchInput) {
      // No need to do anything else, just update the branch name
      return;
    }
  };

  // Check if repository name is valid
  const isValidRepoName = () => {
    return validateRepoName(newRepoName);
  };

  // Check if repository name already exists
  const repoNameExists = () => {
    if (!newRepoName) return false;
    return repositories.some(repo =>
      repo.name.toLowerCase() === newRepoName.toLowerCase()
    );
  };

  // Check if the selected account is a collaborator account
  const isCollaboratorAccount = () => {
    const selectedAccount = installations.find(i => i.installation_id === selectedInstallation);
    return selectedAccount?.account_type === "User" && !selectedAccount?.isPrimary;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!jobId || !selectedInstallation) {
      return;
    }

    if (podIsPaused) {
      toast({
        title: "Pod is paused",
        description: "Please unpause your pod before pushing to GitHub",
        variant: "destructive"
      });
      handleOpenChange(false);
      return;
    }

    const accountLogin = getAccountLogin();
    const repoName = getRepoName();

    if (!accountLogin || !repoName) {
      return;
    }

    const params: GitHubPushParams = {
      jobId,
      accountLogin,
      repoName,
      branchName,
      isNewRepo: isCreatingNewRepo,
      force: needsForcePush
    };

    const result = await pushToGitHub(params);
    if (result && onSuccess) {
      // Pass the repository details to the onSuccess callback
      const repoDetails = {
        branch: branchName,
        repo: repoName,
        owner: accountLogin,
        provider: "github"
      };
      onSuccess(repoDetails);
      // Close the modal after a short delay to show success state
      handleOpenChange(false);
    }
  };

  const handleForcePush = async () => {
    if (!jobId || !selectedInstallation) {
      return;
    }

    const accountLogin = getAccountLogin();
    const repoName = getRepoName();

    if (!accountLogin || !repoName) {
      return;
    }

    const params: GitHubPushParams = {
      jobId,
      accountLogin,
      repoName,
      branchName,
      isNewRepo: isCreatingNewRepo,
      force: true
    };

    const result = await pushToGitHub(params);
    if (result && onSuccess) {
      // Pass the repository details to the onSuccess callback
      const repoDetails = {
        branch: branchName,
        repo: repoName,
        owner: accountLogin,
        provider: "github"
      };
      onSuccess(repoDetails);
      // Close the modal after a short delay to show success state
      handleOpenChange(false);
    }
  };

  const isLoading = initialLoading || isLoadingInstallations || isLoadingRepositories;

  // Handle adding a new GitHub account
  const handleAddNewAccount = async () => {
    // Set connecting state to true
    setIsConnecting(true);

    // Define a function to directly check user details and update UI
    const checkUserDetailsAndUpdateUI = async () => {
      try {
        //console.log("Directly checking user details from API...");
        // Get user details directly from the API
        const userDetails = await agentApi.getUserDetails();
        //console.log("User details response:", userDetails);

        // Check if GitHub is authorized in the response
        if (userDetails && userDetails.github && userDetails.github.authorized === true) {
          //console.log("GitHub is authorized according to API response");

          // Explicitly update the connection state
          setIsConnected(true);

          // Fetch installations
          await fetchInstallations();

          // Show success toast
          toast({
            title: "Success",
            description: "Successfully connected to GitHub",
          });

          // Force reload the modal to show the connected state
          handleOpenChange(false);
          setTimeout(() => {
            handleOpenChange(true);
          }, 100);

          return true;
        } else {
          //console.log("GitHub is not authorized according to API response");
          return false;
        }
      } catch (error) {
        console.error("Error checking user details:", error);
        return false;
      } finally {
        // Always reset connecting state
        setIsConnecting(false);
      }
    };

    // Set up a polling mechanism to check for connection
    const pollForConnection = () => {
      //console.log("Starting to poll for GitHub connection...");
      let attempts = 0;
      const maxAttempts = 10;
      const pollInterval = 3000; // 3 seconds

      const pollTimer = setInterval(async () => {
        attempts++;
        //console.log(`Polling attempt ${attempts}/${maxAttempts}`);

        const connected = await checkUserDetailsAndUpdateUI();

        if (connected || attempts >= maxAttempts) {
          clearInterval(pollTimer);
          if (!connected && attempts >= maxAttempts) {
            //console.log("Max polling attempts reached without success");
            setIsConnecting(false);
          }
        }
      }, pollInterval);

      // Clear interval after a maximum time to prevent infinite polling
      setTimeout(() => {
        clearInterval(pollTimer);
        setIsConnecting(false);
      }, pollInterval * maxAttempts + 1000);
    };

    if (isElectron) {
      // Electron-specific implementation using IPC
      // Type assertion to handle the mismatch between implementation and type definition
      const result = await ipc.initiateGitHubAuth() as unknown as { success: boolean; error?: string };

      if (!result || !result.success) {
        toast({
          title: "Error",
          description: (result && result.error) || "Failed to connect to GitHub",
          variant: "destructive",
        });
        // Reset connecting state on error
        setIsConnecting(false);
      } else {
        // Start polling for connection status
        pollForConnection();
      }
    } else {
      try {
        // Web implementation using the centralized method from GitHubContext
        redirectToGitHubInstallation({
          isPopup: true,
          pollingEnabled: false, // We'll handle polling ourselves
          onSuccess: async () => {
            //console.log("GitHub connection callback triggered");
            await checkUserDetailsAndUpdateUI();
          }
        });

        // Start polling for connection status
        pollForConnection();
      } catch (err) {
        console.error("Error connecting to GitHub:", err);
        setIsConnecting(false);
        toast({
          title: "Error",
          description: "Failed to connect to GitHub",
          variant: "destructive",
        });
      }
    }
  };

  // Handle clicking outside of dropdowns
  const handleOutsideClick = (e: React.MouseEvent<HTMLFormElement>) => {
    // Get the target element
    const target = e.target as HTMLElement;

    // Check if the click is outside of the installation dropdown
    if (showInstallationDropdown &&
        !target.closest('.installation-dropdown') &&
        !target.closest('.installation-input')) {
      setShowInstallationDropdown(false);
    }

    // Check if the click is outside of the repository dropdown
    if (showRepositoryDropdown &&
        !target.closest('.repository-dropdown') &&
        !target.closest('.repository-input') &&
        !target.closest('.repo-toggle-buttons')) {
      setShowRepositoryDropdown(false);
    }

    // Check if the click is outside of the branch dropdown
    if (showBranchDropdown &&
        !target.closest('.branch-dropdown') &&
        !target.closest('.branch-input')) {
      setShowBranchDropdown(false);
    }

    // Check if the click is outside of the custom branch input
    if (showCustomBranchInput &&
        !target.closest('.branch-dropdown') &&
        !target.closest('.branch-input') &&
        !target.closest('#branch-selector')) {
      // Just close the custom branch input without applying the custom branch name
      setShowCustomBranchInput(false);
      // Reset the custom branch name
      setCustomBranchName('');
    }
  };

  // Handle repository name input change (for search or creating new repo)
const handleRepositoryInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const value = e.target.value;
  // Only replace spaces with hyphens, but allow all other valid GitHub repository characters
  const formattedValue = replaceSpacesWithHyphens(value);

  setSearchQuery(formattedValue);

  // Always clear selected repository when search value changes
  if (formattedValue !== searchQuery) {
    setSelectedRepository('');
    setBranches([]);
    // setFilteredBranches removed
    setBranchName('main');
  }

  // If in "Create New" mode, update the new repo name
  if (isCreatingNewRepo) {
    setNewRepoName(formattedValue);
    setRepoNotFound(false);
    return;
  }

  // If in "Select Repo" mode, check if the input matches an existing repo
  const matchingRepo = repositories.find(repo =>
    repo.name.toLowerCase() === formattedValue.toLowerCase()
  );

  if (matchingRepo) {
    // If we found a matching repo, select it
    setSelectedRepository(matchingRepo.name);
    setShowRepositoryDropdown(false);
    setRepoNotFound(false);

    // Also fetch branches for this repository
    fetchBranches(matchingRepo.name);
  } else if (formattedValue) {
    // If no match but we have input, keep the dropdown open to show filtered results
    setSelectedRepository('');
    setShowRepositoryDropdown(true);

    // Check if there are any filtered results
    const hasFilteredResults = repositories.some(repo =>
      repo.name.toLowerCase().includes(formattedValue.toLowerCase())
    );

    // Set repoNotFound to true only if there are no filtered results and we have input
    setRepoNotFound(!hasFilteredResults && formattedValue.length > 0);

    // Clear branches since we don't have a selected repository
    setBranches([]);
  } else {
    // Empty input, clear selection
    setSelectedRepository('');
    setRepoNotFound(false);

    // Clear branches since we don't have a selected repository
    setBranches([]);
  }
};

// Handle lastGithubUsed for installation selection
useEffect(() => {
  if (installations.length > 0 && !isLoadingInstallations) {
    // First check if we have a primary account
    const primaryAccount = installations.find(
      installation => installation.isPrimary
    );

    // If we have lastGithubUsed data, try to match with that first
    if (lastGithubUsed && lastGithubUsed.owner) {
      //console.log('Last GitHub used:', lastGithubUsed);

      // Find and select the matching installation based on owner
      const matchingInstallation = installations.find(
        installation => installation.account_login === lastGithubUsed.owner
      );

      if (matchingInstallation) {
        //console.log('Found matching installation:', matchingInstallation);
        setSelectedInstallation(matchingInstallation.installation_id);
        return;
      } else {
        //console.log('No matching installation found for owner:', lastGithubUsed.owner);
      }
    }

    // If no matching installation from lastGithubUsed, select primary account if available
    if (primaryAccount) {
      //console.log('Selecting primary account:', primaryAccount);
      setSelectedInstallation(primaryAccount.installation_id);
    } else {
      // Otherwise, select a default (organization first, then user account, then first in list)
      const defaultInstallation = installations.find(
        installation => installation.account_type === "Organization"
      ) || installations.find(
        installation => installation.account_type === "User"
      ) || installations[0];

      if (defaultInstallation) {
        //console.log('Selecting default installation:', defaultInstallation);
        setSelectedInstallation(defaultInstallation.installation_id);
      }
    }
  }
}, [lastGithubUsed, installations, isLoadingInstallations]);

// Handle lastGithubUsed for repository selection
useEffect(() => {
  if (lastGithubUsed && !isLoadingRepositories && repositories.length > 0 && selectedInstallation) {
    // Only prefill if the last used repo belongs to the currently selected installation
    const currentInstallation = installations.find(
      installation => installation.installation_id === selectedInstallation
    );

    // Check if the current installation matches the last used owner
    if (currentInstallation && currentInstallation.account_login === lastGithubUsed.owner) {
      // Just use the repo name instead of full_name
      const repoName = lastGithubUsed.repo;
      //console.log('Last GitHub repo used:', repoName, 'for installation:', currentInstallation.account_login);
      setSelectedRepository(repoName);
      setSearchQuery(repoName);

      // Also set the branch name if available
      if (lastGithubUsed.branch) {
        //console.log('Setting branch name to:', lastGithubUsed.branch);
        setBranchName(lastGithubUsed.branch);
      }
    } else {
      //console.log('Last used repo does not match current installation, not prefilling');
      // Reset to default branch name since we're not using the last repo
      setBranchName('main');
    }
  }
}, [lastGithubUsed, repositories, isLoadingRepositories, selectedInstallation, installations]);


if (!isConnected) {
  return <>
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="md:max-w-[620px] md:min-h-[600px] bg-[#0F0F10] overflow-hidden"
      // @ts-ignore
      hideclosebutton
      >
        {/* Loading overlay when connecting */}
        {isConnecting && (
          <div className="absolute inset-0 z-50 flex flex-col items-center justify-center bg-[#0F0F10]/90 backdrop-blur-sm">
            <div className="flex flex-col items-center justify-center p-6 rounded-lg">
              <Loader2 className="w-12 h-12 text-[#00E573] animate-spin mb-4" />
              <p className="text-lg font-medium text-white">Connecting to GitHub...</p>
            </div>
          </div>
        )}

        <img src={LineSVG} alt="Line" className="absolute top-0 left-0 right-0 z-[-1] w-full pointer-events-none" />
        <DialogClose className="z-20 ml-auto">
            <X className="absolute z-20 w-5 h-5 text-gray-400 hover:text-white top-12 right-12" />
          </DialogClose>
        <div className="flex items-center p-[40px] pt-[80px] gap-1 z-10">
          <img src={ConnectWithGithub} alt="GitHub" />
        </div>
        <div className="p-[40px] pt-[0px] flex flex-col items-start gap-4 h-[300px] z-10">
          <span className="text-white text-[24px] font-medium font-brockmann">Connect Github to Emergent</span>
          <span className='text-[18px] text-[#8A8F98]'>
          Connect your GitHub account to effortlessly save, sync, and access your work in all repositories, anytime.
          </span>
        </div>
        <div className='flex items-center justify-end gap-4 border border-[#242424] p-[20px] bg-[#111112]'>
        <Button
          onClick={() => handleOpenChange(false)}
          className="flex items-center px-[20px] w-fit justify-center bg-[#272729] hover:bg-[#343434] text-white"
          disabled={isConnecting}
        >
            <div className='flex items-center justify-center w-full text-[16px]'>
              Cancel
            </div>
          </Button>
        <Button
          onClick={handleAddNewAccount}
          className="max-w-[200px] w-full bg-[#00E573] hover:bg-[##00E573]/90 text-[#0E0E0F] font-brockmann font-semibold hover:bg-[#00E573]/80"
          disabled={isConnecting}
        >
          <div className='flex items-center justify-center w-full text-[16px]'>
            {isConnecting ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                Connecting...
              </div>
            ) : (
              "Connect GitHub"
            )}
          </div>
        </Button>
        </div>
      </DialogContent>
    </Dialog>
  </>;
}

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent
        className="max-w-[700px] min-h-[600px] p-0 overflow-visible transition-all duration-300 ease-in-out top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        //@ts-ignore
        hideclosebutton
      >
        {/* Loading overlay when connecting */}
        {isConnecting && (
          <div className="absolute inset-0 z-50 flex flex-col items-center justify-center bg-[#0F0F10]/90 backdrop-blur-sm">
            <div className="flex flex-col items-center justify-center p-6 rounded-lg">
              <Loader2 className="w-12 h-12 text-[#00E573] animate-spin mb-4" />
              <p className="text-lg font-medium text-white">Connecting to GitHub...</p>
            </div>
          </div>
        )}

        <div className="flex items-center gap-2 px-8 max-h-[98px] py-6 border-b border-[#242424]">
          <div className="flex items-center justify-center bg-[#ffffff05] p-2 rounded-full">
            <img
              src={GithubIconFull}
              alt="GitHub"
              className="w-6 h-6"
            />
          </div>
          <h2 className="text-xl font-medium text-white">Save to Github</h2>
          <DialogClose className="ml-auto">
            <X className="w-5 h-5 text-gray-400 hover:text-white" />
          </DialogClose>
        </div>

        {/* Always render the form, but conditionally show loading overlay */}
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-[#0F0F10] transition-opacity duration-300 ease-in-out">
              <Loader2 className="h-8 w-8 text-[#00FF85] animate-spin mb-4" />
              <p className="text-[#DDDDE6]/70 text-sm">
                {initialLoading
                  ? "Connecting to GitHub..."
                  : isLoadingInstallations
                    ? "Loading GitHub accounts..."
                    : "Loading repositories..."}
              </p>
            </div>
          )}
          <form
            onSubmit={handleSubmit}
            className="p-8 space-y-6"
            onClick={handleOutsideClick}
          >
            {/* Connected Account Section */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <img
                  src={GithubIcon}
                  alt="GitHub"
                  className="w-5 h-5 opacity-70"
                />
                <span className="text-sm text-gray-400">
                  Connected Organizations
                </span>
              </div>
              <div className="relative">
                <div className={`flex items-center justify-between w-full h-14 bg-[#131314] border ${showInstallationDropdown ? 'border-white/50' : 'border-[#242424]'} rounded-md p-3 text-white installation-input`}
                  onClick={() => {
                    // Close other dropdowns when clicking on the installation input
                    setShowRepositoryDropdown(false);
                    setShowBranchDropdown(false);
                    // Toggle installation dropdown
                    setShowInstallationDropdown(!showInstallationDropdown);
                  }}
                >
                  <div className="flex items-center w-full">
                    <div className="relative flex-1">
                      {selectedInstallation ? (
                        <div className="flex items-center">
                          {installations.find(i => i.installation_id === selectedInstallation)?.account_login || "Select a GitHub account"}
                          {installations.find(i => i.installation_id === selectedInstallation)?.account_type === "User" &&
                           !installations.find(i => i.installation_id === selectedInstallation)?.isPrimary && (
                            <span className="text-xs text-[#DDDDE6] rounded-full bg-[#FFFFFF0D] ml-1 px-2 py-1 font-['Inter'] font-medium">Collaborator</span>
                          )}
                        </div>
                      ) : (
                        <span className="text-[#8F8F98]">Select a GitHub account</span>
                      )}
                    </div>
                  </div>
                  <ChevronDown
                    className={`h-4 w-4 text-[#898F99] transition-transform duration-200 ${
                      showInstallationDropdown ? "rotate-180" : ""
                    }`}
                  />
                </div>

                {/* Installation dropdown */}
                {installations.length > 0 && showInstallationDropdown && (
                  <div className="absolute z-[100] w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg max-h-[300px] overflow-y-auto installation-dropdown">
                    <div className="flex flex-col gap-[6px] p-2">
                      {installations.map((installation) => (
                        <div
                          key={installation.installation_id}
                          className={cn(
                            "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                            selectedInstallation === installation.installation_id && "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]",
                          )}
                          onClick={() => {
                            // If we're selecting the same installation, don't reset everything
                            if (installation.installation_id === selectedInstallation) {
                              setShowInstallationDropdown(false);
                              return;
                            }

                            // Clear repository-related state when changing installation
                            setSelectedRepository('');
                            setSearchQuery(''); // Clear the search query/text field
                            setBranches([]);
                            setIsCreatingNewRepo(false);
                            setNewRepoName('');
                            setRepoNotFound(false);

                            // Close any open dropdowns
                            setShowRepositoryDropdown(false);
                            setShowBranchDropdown(false);
                            setShowInstallationDropdown(false);

                            setSelectedInstallation(installation.installation_id);

                            // Force the input field to clear by setting an empty value
                            const repoInput = document.querySelector('.repository-input input') as HTMLInputElement;
                            if (repoInput) {
                              repoInput.value = '';
                            }
                          }}
                        >
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-2">
                              <span>{installation.account_login}</span>
                              {installation.account_type === "User" && !installation.isPrimary && (
                                <span className="text-xs text-[#DDDDE6] rounded-full bg-[#FFFFFF0D] ml-1 px-2 py-1 font-['Inter'] font-medium">Collaborator</span>
                              )}
                            </div>
                            {selectedInstallation === installation.installation_id && (
                              <Check className="h-4 w-4 text-[#1BB4CC]" />
                            )}
                          </div>
                        </div>
                      ))}
                      <div className="border-t border-[#ffffff12]"></div>
                      <div
                        className={`px-3 min-h-[40px] rounded-[8px] py-2 text-[#4ADE80] hover:bg-[#4ADE80]/10 font-medium ${isConnecting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                        onClick={() => {
                          if (!isConnecting) {
                            handleAddNewAccount();
                            setShowInstallationDropdown(false);
                          }
                        }}
                      >
                        <div className="flex items-center gap-2">
                          {isConnecting ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <Plus className="w-4 h-4" />
                          )}
                          {isConnecting ? "Connecting..." : "Add New Github Organizations"}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              {installations.length === 0 && !initialLoading && !isLoadingInstallations && (
                <p className="text-[#FF4545] text-sm">
                  No GitHub accounts connected. Please connect a GitHub account.
                </p>
              )}
            </div>

            {/* Repository Section */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center justify-between w-full gap-2">
                  <div className="flex items-center gap-2">
                    <img
                      src={RepositoryIcon}
                      alt="Repository"
                      className="w-5 h-5"
                    />
                    <span className="text-[#898F99] text-sm font-medium font-[Inter]">
                      Selected Repo
                    </span>
                  </div>
                  {selectedInstallation && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="w-6 h-6 ml-1 rounded-full"
                      onClick={handleRefreshRepositories}
                      disabled={isLoadingRepositories || isRefreshing}
                      title="Refresh repositories"
                    >
                      <RefreshCw
                        className={`h-3.5 w-3.5 text-[#898F99] ${
                          isRefreshing ? "animate-spin" : ""
                        }`}
                      />
                    </Button>
                  )}
                </div>
              </div>

              <div className="relative">
                <div className={`flex items-center justify-between w-full h-14 bg-[#131314] border ${showRepositoryDropdown ? 'border-white/50' : 'border-[#242424]'} rounded-md p-3 text-white repository-input`}>
                  <div className="flex items-center w-full">
                    <Search className="h-4 w-4 text-[#8F8F98] mr-2" />
                    <div className="relative flex-1">
                      <Input
                        type="text"
                        value={searchQuery}
                        onChange={handleRepositoryInputChange}
                        onFocus={() => {
                          if (!isCreatingNewRepo) {
                            // Close other dropdowns
                            setShowBranchDropdown(false);
                            setShowInstallationDropdown(false);
                            // Open repository dropdown
                            setShowRepositoryDropdown(true);
                          }
                        }}
                        placeholder={isCreatingNewRepo ? "Enter new repository name" : "Search for a repository"}
                        className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                        disabled={
                          !selectedInstallation ||
                          (isCreatingNewRepo && isCollaboratorAccount())
                        }
                      />
                      {selectedRepository && !isCreatingNewRepo && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 w-6 h-6 transform -translate-y-1/2 rounded-full top-1/2"
                          onClick={() => {
                            setSelectedRepository('');
                            setSearchQuery('');
                            setBranches([]);
                            // setFilteredBranches removed
                            setBranchName('main');
                            setShowRepositoryDropdown(true);
                          }}
                          title="Clear selected repository"
                        >
                          <X className="h-3.5 w-3.5 text-[#898F99]" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <div className="flex text-sm text-nowrap bg-[#ffffff15] p-[6px] rounded-full font-semibold cursor-pointer repo-toggle-buttons">
                    <span
                      className={`px-[10px] py-1 rounded-full transition-colors ${!isCreatingNewRepo ? 'bg-white text-black' : 'text-white/50 hover:text-white/80 font-medium'}`}
                      onClick={() => {
                        setIsCreatingNewRepo(false);

                        // Close other dropdowns
                        setShowBranchDropdown(false);
                        setShowInstallationDropdown(false);

                        // Always show the repository dropdown when clicking "Select Repo"
                        if (selectedInstallation && repositories.length > 0) {
                          setShowRepositoryDropdown(true);
                        }

                        // If we have a search query, try to find a matching repository
                        if (searchQuery) {
                          const matchingRepo = repositories.find(r =>
                            r.name.toLowerCase() === searchQuery.toLowerCase()
                          );

                          if (matchingRepo) {
                            // If we found a matching repo, select it
                            setSelectedRepository(matchingRepo.name);
                            setSearchQuery(matchingRepo.name);
                            setRepoNotFound(false);

                            // Also fetch branches for this repository
                            fetchBranches(matchingRepo.name);
                          } else {
                            // If no matching repo, show the error
                            setSelectedRepository('');
                            setRepoNotFound(true);
                            setBranches([]);
                            // setFilteredBranches removed
                            setBranchName('main');
                          }
                        } else if (selectedRepository) {
                          // If we already have a selected repository, keep it
                          const repo = repositories.find(r => r.name === selectedRepository);
                          if (repo) {
                            setSearchQuery(repo.name);
                            setRepoNotFound(false);
                          } else {
                            // If repository no longer exists, clear it
                            setSelectedRepository('');
                            setBranches([]);
                            // setFilteredBranches removed
                            setBranchName('main');
                          }
                        } else {
                          // No repository selected, clear branch data
                          setBranches([]);
                          // setFilteredBranches removed
                          setBranchName('main');
                        }
                      }}
                    >
                      Select Repo
                    </span>
                    <span
                      className={`px-[10px] py-1 rounded-full transition-colors ${
                        isCreatingNewRepo ? 'bg-white text-black ' :
                        isCollaboratorAccount() ?
                        'text-white/20 cursor-not-allowed' : 'text-white/50 hover:text-white/80 font-medium'
                      }`}
                      onClick={() => {
                        // Check if the selected account is a collaborator account
                        if (isCollaboratorAccount()) {
                          return;
                        }

                        // Switch to create new mode
                        setIsCreatingNewRepo(true);

                        // Close any open dropdowns
                        setShowRepositoryDropdown(false);
                        setShowBranchDropdown(false);
                        setShowInstallationDropdown(false);

                        // Clear selected repository and error state
                        setSelectedRepository('');
                        setRepoNotFound(false);

                        // If we have a search query, use it as the new repo name
                        if (searchQuery) {
                          setNewRepoName(searchQuery);
                        } else {
                          // If no search query but we have a selected repository, use that
                          const repo = repositories.find(r => r.name === selectedRepository);
                          if (repo) {
                            setNewRepoName(repo.name);
                            setSearchQuery(repo.name);
                          }
                        }

                        // Reset branches since we're creating a new repo
                        setBranches([]);
                        // setFilteredBranches removed
                        setBranchName('main');
                      }}
                    >
                      Create New
                    </span>
                  </div>
                </div>

                {/* Repository dropdown */}
                {repositories.length > 0 &&
                  filteredRepositories.length > 0 &&
                  showRepositoryDropdown &&
                  !isCreatingNewRepo && (
                    <div className="absolute z-[100] w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg max-h-[300px] overflow-y-auto repository-dropdown">
                      <div className="px-2 py-1 flex flex-col gap-[6px]">
                        {filteredRepositories.map((repo) => (
                          <div
                            key={repo.id}
                            className={cn(
                              "px-3 min-h-[40px] rounded-[8px]   py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium  cursor-pointer",
                              repo.permissions.push ? "" : "opacity-20",
                              selectedRepository === repo.name &&
                                "bg-[#172426] text-[#1BB4CC]"
                            )}
                            onClick={() => {
                              if (repo.permissions.push) {
                                setSelectedRepository(repo.name);
                                setSearchQuery(repo.name);
                                setIsCreatingNewRepo(false);
                                setNewRepoName("");
                                setShowRepositoryDropdown(false);
                                setRepoNotFound(false);

                                // Fetch branches for the selected repository
                                fetchBranches(repo.name);
                              } else {
                                toast({
                                  title:
                                    "You don't have push access to this repository.",
                                  description:
                                    "Please select a repository you have push access to.",
                                  variant: "destructive",
                                });
                              }
                            }}
                          >
                           <div className='flex items-center justify-between'>
                           <span>{repo.name}</span>

                            {lastGithubUsed?.repo === repo.name &&
                             lastGithubUsed?.owner === getAccountLogin() && (
                              <Badge
                                variant="secondary"
                                className="bg-green-700"
                              >
                                Last used
                              </Badge>
                            )}

                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* Feedback messages */}
                {selectedInstallation &&
                  isCreatingNewRepo &&
                  isCollaboratorAccount() && (
                  <p className="text-[#FF4545] text-sm mt-2 flex items-center gap-1">
                    <img
                      src={InfoSquareIcon}
                      alt="Info"
                      className="w-3 h-3"
                    />
                    <span>
                      You don't have permission to create repositories in an organization where you're a collaborator.
                    </span>
                  </p>
                )}
                {selectedInstallation && isCreatingNewRepo && !newRepoName && (
                  <p className="text-[#DDDDE6]/50 text-sm mt-2">
                    Enter a name for your new repository
                  </p>
                )}
                {selectedInstallation &&
                  isCreatingNewRepo &&
                  newRepoName &&
                  !isValidRepoName() && (
                    <p className="text-[#FF4545] text-sm mt-2">
                      Invalid repository name. Names must follow Git naming rules: only alphanumeric characters and - . / _ are allowed. Cannot contain spaces, consecutive dots (..), begin/end with /, end with ., or contain special characters.
                    </p>
                  )}
                {selectedInstallation &&
                  isCreatingNewRepo &&
                  newRepoName &&
                  isValidRepoName() && (
                    <p className="text-[#CCC] text-sm mt-2 flex items-center gap-1">
                      <img
                        src={InfoSquareIcon}
                        alt="Info"
                        className="w-3 h-3"
                      />
                      <span>
                        {repoNameExists() ? (
                          <>
                            Repository <span className="font-semibold text-[#FF4545]">{newRepoName}</span> already exists.
                          </>
                        ) : (
                          <>
                            New repo{" "}
                            <span className="font-semibold text-[#00FF85]">
                              {newRepoName}
                            </span>{" "}
                            will be created and your current changes will be pushed to it.
                          </>
                        )}
                      </span>
                    </p>
                  )}
                {selectedInstallation && repositories.length === 0 && (
                  <p className="text-[#DDDDE6]/50 text-sm mt-2">
                    No repositories found. Enter a name to create a new one.
                  </p>
                )}
                {selectedInstallation && !isCreatingNewRepo && repoNotFound && searchQuery && (
                  <p className="text-[#FF4545] text-sm mt-2 flex items-center gap-1">
                    <img
                      src={InfoSquareIcon}
                      alt="Info"
                      className="w-3 h-3"
                    />
                    <span>
                      Repository <span className="font-semibold">"{searchQuery}"</span> doesn't exist. Switch to "Create New" to create it.
                    </span>
                  </p>
                )}
              </div>
            </div>

            {/* Branch Selection */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <img src={BranchIcon} alt="Branch" className="w-5 h-5" />
                  <span className="text-[#898F99] text-sm font-medium font-[Inter]">
                    Branch
                  </span>
                </div>
                {selectedRepository && !isCreatingNewRepo && (
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="w-6 h-6 ml-1 rounded-full"
                      onClick={handleRefreshBranches}
                      disabled={isLoadingBranches}
                      title="Refresh branches"
                    >
                      <RefreshCw
                        className={`h-3.5 w-3.5 text-[#898F99] ${
                          isLoadingBranches ? "animate-spin" : ""
                        }`}
                      />
                    </Button>
                  </div>
                )}
              </div>

              {isLoadingBranches ? (
                <div className="flex items-center justify-center h-14 bg-[#131314] border border-[#242424] rounded-md">
                  <Loader2 className="h-4 w-4 text-[#00FF85] animate-spin" />
                </div>
              ) : isCreatingNewRepo ? (
                <div className="space-y-2">
                  <div className="relative">
                    <div className="flex items-center justify-between w-full h-14 bg-[#131314] border border-[#242424] rounded-md p-3 text-white">
                      <div className="flex items-center w-full branch-input">
                        <div className="relative flex-1">
                          <Input
                            type="text"
                            value={branchName}
                            onChange={handleBranchInputChange}
                            placeholder="Branch name (e.g., main)"
                            className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                            disabled={
                              !selectedInstallation ||
                              (!newRepoName && isCreatingNewRepo)
                            }
                          />
                          {branchName && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 w-6 h-6 transform -translate-y-1/2 rounded-full top-1/2"
                              onClick={() => {
                                setBranchName('main');
                              }}
                              title="Clear branch name"
                              disabled={
                                !selectedInstallation ||
                                (!newRepoName && isCreatingNewRepo)
                              }
                            >
                              <X className="h-3.5 w-3.5 text-[#898F99]" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  {branchName && !validateGitName(branchName) && (
                    <div className="text-[#FF4545] text-sm mt-2">
                      Invalid branch name. Names must follow Git naming rules: only alphanumeric characters and - . / _ are allowed. Cannot contain spaces, consecutive dots (..), begin/end with /, end with ., or contain special characters.
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="relative">
                    <div className="relative">
                      <div
                        className={`flex items-center justify-between w-full h-14 bg-[#131314] border ${showBranchDropdown ? 'border-white/50' : 'border-[#242424]'} rounded-md p-3 text-white branch-input`}
                        onClick={() => {
                          if (!selectedInstallation || !selectedRepository || branches.length === 0) {
                            return;
                          }
                          // Close other dropdowns when clicking on the branch input
                          setShowRepositoryDropdown(false);
                          setShowInstallationDropdown(false);
                          // Toggle branch dropdown
                          setShowBranchDropdown(!showBranchDropdown);
                        }}
                        id="branch-selector"
                      >
                        <div className="flex items-center w-full">
                          <div className="relative flex-1">
                            {branchName ? (
                              <div className="flex items-center">
                                {branchName}
                              </div>
                            ) : (
                              <span className="text-[#8F8F98]">Select a branch</span>
                            )}
                          </div>
                        </div>
                        <ChevronDown
                          className={`h-4 w-4 text-[#898F99] transition-transform duration-200 ${
                            showBranchDropdown ? "rotate-180" : ""
                          }`}
                        />
                      </div>

                      {/* Branch dropdown */}
                      {branches.length > 0 && showBranchDropdown && (
                        <div className="absolute z-[100] w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg max-h-[300px] overflow-y-auto branch-dropdown">
                          <div className="flex flex-col gap-[6px] p-2">
                            {branches.map((branch) => (
                              <div
                                key={branch.name}
                                className={cn(
                                  "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                                  branchName === branch.name && "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                                )}
                                onClick={() => {
                                  setBranchName(branch.name);
                                  setShowBranchDropdown(false);
                                  // Clear other dropdowns
                                  setShowRepositoryDropdown(false);
                                  setShowInstallationDropdown(false);
                                }}
                              >
                                <div className="flex items-center justify-between w-full">
                                  <span>{branch.name}</span>
                                  {branchName === branch.name && (
                                    <Check className="h-4 w-4 text-[#1BB4CC]" />
                                  )}
                                </div>
                              </div>
                            ))}
                            <div className="border-t border-[#ffffff12] mt-2"></div>
                            <div
                              className="px-3 min-h-[40px] rounded-[8px] py-2 text-[#4ADE80] hover:bg-[#4ADE80]/10 font-medium cursor-pointer"
                              onClick={() => {
                                // Initialize the custom branch name with the current branch name
                                // or a default value if no branch is selected
                                setCustomBranchName(branchName || 'main');
                                // Show the custom branch input field
                                setShowCustomBranchInput(true);
                                // Close the branch dropdown
                                setShowBranchDropdown(false);
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <Plus className="w-4 h-4" />
                                Create New Branch
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Custom branch input - shown when "Create New Branch" is selected */}
                    {showCustomBranchInput && (
                      <div className="absolute z-[100] w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg branch-dropdown">
                        <div className="p-3 space-y-3">
                          <div className="text-sm text-[#898F99]">Enter a new branch name:</div>
                          <div className="flex items-center w-full branch-input">
                            <div className="relative flex-1">
                              <Input
                                type="text"
                                value={customBranchName}
                                onChange={(e) => {
                                  const value = replaceSpacesWithHyphens(e.target.value);
                                  setCustomBranchName(value);
                                }}
                                placeholder="Branch name (e.g., feature/my-new-branch)"
                                className="w-full p-2 bg-[#1A1A1C] border border-[#242424] rounded-md focus-visible:ring-0 focus-visible:border-white/50 focus-visible:ring-offset-0"
                                autoFocus
                              />
                            </div>
                          </div>
                          {customBranchName && !validateGitName(customBranchName) && (
                            <div className="text-[#FF4545] text-xs">
                              Invalid branch name. Names must follow Git naming rules: only alphanumeric characters and - . / _ are allowed.
                            </div>
                          )}
                          <div className="flex justify-end gap-2 mt-3">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                // Just close the custom branch input without changing the branch name
                                setShowCustomBranchInput(false);
                                // Reset the custom branch name
                                setCustomBranchName('');
                              }}
                              className="border-[#242424] text-gray-300 hover:bg-[#1a1a1c] bg-transparent"
                            >
                              Cancel
                            </Button>
                            <Button
                              type="button"
                              size="sm"
                              onClick={() => {
                                // Only close if the custom branch name is valid
                                if (validateGitName(customBranchName)) {
                                  // Update the actual branch name with the custom branch name
                                  setBranchName(customBranchName);
                                  setShowCustomBranchInput(false);
                                }
                              }}
                              disabled={!validateGitName(customBranchName)}
                              className="bg-[#00FF85] hover:bg-[#00FF85]/90 text-black font-medium"
                            >
                              Use Branch
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  {branchName && !validateGitName(branchName) && (
                    <div className="text-[#FF4545] text-sm mt-2">
                      Invalid branch name. Names must follow Git naming rules: only alphanumeric characters and - . / _ are allowed. Cannot contain spaces, consecutive dots (..), begin/end with /, end with ., or contain special characters.
                    </div>
                  )}
                  {branchName && validateGitName(branchName) && !branchExists && selectedRepository && (
                    <div className="text-[#898F99] text-sm font-medium font-[Inter] flex items-center gap-1">
                      <img
                        src={InfoSquareIcon}
                        alt="Info"
                        className="w-3 h-3"
                      />
                      <span>
                        New branch{" "}
                        <span className="text-[#00FF85]">'{branchName}'</span>{" "}
                        will be created and your current changes will be pushed
                        to it.
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {error && (
              <div className={`rounded-md`}>
                {needsForcePush ? (
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <img
                        src={WarningPurple}
                        alt="Warning"
                        className="w-5 h-5"
                      />
                      <h4 className="text-[#B266FF] font-medium text-[14px] font-[Inter] leading-[20px]">
                        Conflict Detected
                      </h4>
                    </div>
                    <p className="text-[#898F99] font-medium text-[14px] font-[Inter] leading-[20px]">
                      {error.includes("\n")
                        ? error.split("\n")[1]
                        : "Changes on the remote branch conflict with your changes. You can force push to overwrite the remote branch."}
                    </p>
                  </div>
                ) : (
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-400" />
                      <h4 className="text-red-400 font-medium text-[14px] font-[Inter] leading-[20px]">
                        Error
                      </h4>
                    </div>
                    <p className="text-sm text-red-400">{error}</p>
                  </div>
                )}
              </div>
            )}

            {success && (
              <div className="p-3 border rounded-md bg-green-500/10 border-green-500/30">
                <p className="text-sm text-green-400">
                  Successfully pushed to GitHub!
                </p>
              </div>
            )}
          </form>
        </div>

        <DialogFooter className="flex justify-end p-6 border-t border-[#242424] ">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            className="mr-2 border-[#242424] text-gray-300 hover:bg-[#1a1a1c] bg-transparent"
            disabled={isPushing}
          >
            Cancel
          </Button>
          {needsForcePush ? (
            <Button
              type="button"
              onClick={handleForcePush}
              disabled={
                isPushing ||
                !jobId ||
                !selectedInstallation ||
                (isCreatingNewRepo
                  ? !newRepoName || !isValidRepoName() || repoNameExists()
                  : !selectedRepository) ||
                !branchName || !validateGitName(branchName) ||
                isLoading
              }
              className="bg-[#B266FFF5] hover:bg-[#B266FFF5]/90 text-black font-medium"
            >
              {isPushing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isCreatingNewRepo
                    ? "Creating & Pushing..."
                    : "Force Pushing..."}
                </>
              ) : (
                <div className="flex items-center gap-2">
                  Force Push to Github
                  <img
                    src={SaveCloudIcon}
                    alt="Save Cloud"
                    className="w-6 h-6"
                  />
                </div>
              )}
            </Button>
          ) : (
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={
                isPushing ||
                !jobId ||
                !selectedInstallation ||
                (isCreatingNewRepo
                  ? !newRepoName || !isValidRepoName() || repoNameExists()
                  : !selectedRepository) ||
                !branchName || !validateGitName(branchName) ||
                isLoading
              }
              className="bg-[#00FF85] hover:bg-[#00FF85]/90 text-black font-medium"
            >
              {isPushing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isCreatingNewRepo ? "Creating & Pushing..." : "Pushing..."}
                </>
              ) : (
                <div className="flex items-center gap-2">
                  {isCreatingNewRepo
                    ? "Create & Push to Github"
                    : "Push to Github"}
                  <img
                    src={SaveCloudIcon}
                    alt="Save Cloud"
                    className="w-6 h-6"
                  />
                </div>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
