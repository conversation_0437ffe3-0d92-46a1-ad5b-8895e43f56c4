import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { supabase } from '@/lib/supabase';

function InviteCodeVerification() {
  const [code, setCode] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      const { data } = await supabase.auth.getUser();
      if (data?.user) {
        setUserId(data.user.id);
      } else {
        setError("You must be logged in to verify an invite code");
      }
    };

    fetchUser();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userId) {
      setError("User authentication required");
      return;
    }

    if (!code.trim()) {
      setError("Please enter an invite code");
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Call the Edge Function using supabase.functions
      const { data, error } = await supabase.functions.invoke('verify-invite-code', {
        body: {
          inviteCode: code.trim(),
          userId: userId
        }
      });
      
      if (error) {
        throw new Error(error.message || 'Failed to verify invite code');
      }

      // No need to check response.ok when using supabase.functions.invoke

      if (data.banned) {
        // Handle banned state
        setError("This account has been restricted due to an invalid invite code");
        // Force logout if user is banned
        // await supabase.auth.signOut();
        setTimeout(() => {
          window.location.href = '/banned';
        }, 3000);
        return;
      }

      if (!data.success) {
        throw new Error(data.error || 'Invalid invite code');
      }

      setSuccess(true);
      
      // Redirect after successful verification
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);

    } catch (error: any) {
      setError(error.message || 'An error occurred during verification');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center w-full h-full p-4">
      <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-lg">
        <h2 className="mb-6 text-2xl font-bold text-center">Enter Invite Code</h2>
        
        {success ? (
          <div className="text-center">
            <div className="p-4 mb-4 text-green-700 bg-green-100 rounded-md">
              <p className="font-medium">Code verified successfully!</p>
              <p className="mt-2 text-sm">Redirecting to dashboard...</p>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <label htmlFor="inviteCode" className="block mb-1 text-sm font-medium text-gray-700">
                  Invite Code
                </label>
                <Input
                  id="inviteCode"
                  type="text"
                  placeholder="Enter your invite code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className="w-full"
                  autoComplete="off"
                />
              </div>

              {error && (
                <div className="p-3 text-sm text-red-600 rounded-md bg-red-50">
                  {error}
                </div>
              )}

              <Button 
                type="submit" 
                disabled={loading || !userId} 
                className="w-full"
              >
                {loading ? "Verifying..." : "Verify Code"}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

export default InviteCodeVerification;