import { useState, useEffect } from "react";
import { Dialog, DialogContent } from "../ui/dialog";

import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { agentApi } from "@/services/agentApi";
import { captureError } from "@/services/postHogService";

import NewCoin from "@/assets/new_coin.svg";
import EmergentButton from "../EmergentButton";
import PaymentFailed from "@/assets/payment_failed.svg";

interface PaymentStatusModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  paymentId: string;
  initialStatus: "success" | "cancelled" | "failed";
}

export function PaymentStatusModal({
  isOpen,
  onOpenChange,
  paymentId,
  initialStatus,
}: PaymentStatusModalProps) {
  const [status, setStatus] = useState<
    "loading" | "success" | "pending" | "error"
  >(initialStatus === "success" ? "loading" : "error");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [creditAmount, setCreditAmount] = useState<number>(200);
  const navigate = useNavigate();

  // const confettiRef = useRef<ConfettiRef>(null);

  useEffect(() => {
    // confettiRef.current?.fire({});
    const verifyPayment = async () => {
      if (initialStatus !== "success") {
        setStatus("error");
        setErrorMessage(
          initialStatus === "cancelled"
            ? "Payment was cancelled."
            : "Payment failed to process."
        );

        return;
      }

      try {
        const response = await agentApi.checkPaymentStatus(paymentId);

        // pending, expired, success
        if (response.status === "success") {
          setStatus("success");
          // Set credit amount from response if available
          if (response.ecu_amount) {
            setCreditAmount(response.ecu_amount || 200);
          }
        } else if (response.status === "pending") {
          setStatus("pending");
        } else {
          setStatus("error");
          setErrorMessage(response.error || "Failed to verify payment status.");
        }
      } catch (error) {
        console.error("Error verifying payment:", error);
        captureError("Payment verification failed", {
          paymentId,
          initialStatus,
          error: error instanceof Error ? error.message : String(error),
        });

        setStatus("error");
        setErrorMessage(
          "Failed to verify payment status. Please contact support."
        );
      }
    };

    if (isOpen && initialStatus === "success") {
        verifyPayment();
    }
  }, [isOpen, paymentId, initialStatus]);

  const handleClose = () => {
    onOpenChange(false);
    navigate("/");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[90%] md:max-w-[600px] bg-[#1C1C1F] text-white border-[#2E2F34] md:p-[94px] md:px-[152px]">
        <div className="flex flex-col items-center justify-center py-6">
          {status === "loading" ? (
            // Loading state
            <div className="flex flex-col items-center justify-center gap-[48px] font-brockmann">
              <div className="relative">
                <img src={NewCoin} alt="Coin" className="w-16 h-16" />
                <Loader2 className="w-16 h-16 absolute top-0 left-0 animate-spin text-[#F3CA5F] opacity-70" />
              </div>
              <div className="flex flex-col items-center gap-1">
                <p className="text-[18px] md:text-[32px] md:leading-[36px] font-medium text-white text-nowrap">
                  Processing Payment
                </p>
                <span className="font-['Inter'] mt-2 font-medium text-normal text-[#737780] text-center text-nowrap">
                  Verifying your payment, please wait...
                </span>
              </div>
            </div>
          ) : status === "success" ? (
            // Success state
            <div className="flex flex-col items-center justify-center gap-[48px] font-brockmann">
              <img src={NewCoin} alt="Coin" className="w-16 h-16" />
              <div className="flex flex-col items-center gap-1">
                <p className="text-[18px] md:text-[32px] md:leading-[36px] font-medium text-white">
                  You have received
                </p>
                <span className="text-center text-nowrap text-[28px] md:text-[32px] md:leading-[36px] font-medium bg-gradient-to-b from-[#F3CA5F] to-[#987315] text-transparent bg-clip-text">
                  {creditAmount} add-on Credits
                </span>
                <span className="font-['Inter'] mt-2 font-medium text-normal text-[#737780] text-center text-nowrap">
                  Top-up complete - you're all set to build.
                </span>
              </div>
              <EmergentButton
                onClick={handleClose}
                variant="light"
                className="w-full"
              >
                Start Building
              </EmergentButton>
            </div>
          ) : status === "pending" ? (
            // Pending state
            <div className="flex flex-col items-center justify-center gap-[48px] font-brockmann">
              <img src={NewCoin} alt="Coin" className="w-16 h-16 opacity-50" />
              <div className="flex flex-col items-center gap-1">
                <p className="text-[32px] leading-[36px] font-medium text-white">
                  Payment Pending
                </p>
                <span className="font-['Inter'] mt-2 font-medium text-normal text-[#737780] text-center text-nowrap">
                  Your payment is being processed. This may take a few minutes.
                </span>
              </div>
              <EmergentButton
                onClick={handleClose}
                variant="light"
                className="w-full"
              >
                Continue
              </EmergentButton>
            </div>
          ) : (
            // Error state
            <div className="flex flex-col items-center justify-center gap-[48px] font-brockmann">
              <div className="relative">
                <img src={PaymentFailed} alt="Coin" className="w-16 h-16" />
              </div>
              <div className="flex flex-col items-center gap-1">
                <p className="text-[18px] md:text-[32px] md:leading-[36px] font-medium text-white">
                  Payment Failed
                </p>
                <span className="font-['Inter'] mt-2 font-medium text-normal text-[#737780] text-center">
                  {errorMessage ||
                    "Your payment could not be processed. Please try again."}
                </span>
              </div>
              <EmergentButton
                onClick={handleClose}
                variant="light"
                className="w-full"
              >
                Close
              </EmergentButton>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default PaymentStatusModal;
