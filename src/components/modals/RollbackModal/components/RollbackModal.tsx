import React, { useState, useEffect } from 'react';
import { Check, Clock, Mail, ServerCrash, Undo2, Undo } from "lucide-react";
import { Dialog, DialogContent } from "../../../ui/dialog";
import { Button } from "../../../ui/button";
import ClockFill from '../../../icons/ClockFill';
import { useRollback } from '@/hooks/useRollback';
import { UIMessage } from '@/types/message';
import { Label } from '@/components/ui/label';
import RevertFailedIcon from '@/components/icons/RevertFailed';
import { MAX_RETRY_ATTEMPTS, SUPPORT_EMAIL } from '@/constants/constants';
import { getRetryAttemptsFromLocalStorage } from '@/lib/utils/rollback';


interface RollbackModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isLoading?: boolean;
  isCompleted?: boolean;
  rollbackRequestId: string | undefined;
  containerId: string | undefined;
  jobId: string;
  isCloudFlow: boolean;
  session: any;
  resetPolling: () => void;
  setAiMessages: (messages: UIMessage[]) => void;
}

// Define the rollback steps
const rollbackSteps = [
  { id: 1, label: "Pausing the Agent" },
  { id: 2, label: "Cleaning up the context" },
  { id: 3, label: "Restoring previous workspace state" },
  { id: 4, label: "Finalizing the reversion" }
];

type ReversionStatus = "INIT" | "IN_PROGRESS" | "COMPLETED" | "ERROR" | "FAILED";
type ReversionStatusProps = {
  status: ReversionStatus;
  header: React.ReactNode;
  description: React.ReactNode;
  prompt: React.ReactNode;
  eta?: boolean | string;
  etaMessage?: string;
  buttons: string[];
  attempts?: boolean;
  maxAttempts?: number;
  nextAction?: string;
}


const Header = ({ header }: { header: React.ReactNode }) => {
  return (
    <div className="px-6 pt-20 pb-4">
      <h2 className="text-[24px] font-semibold text-white">{header}</h2>
    </div>
  )
}

const Description = ({ status }: { status: ReversionStatus }) => {
  switch (status) {
    case "INIT":
      return <div className="flex-1 min-h-0 px-4 pt-4 pb-0 overflow-y-auto">
        <p className="text-[16px] text-[#8A8B91] max-w-[600px] mb-4">
          You’re about to revert this conversation to an earlier state. Doing so will permanently delete all <span className="font-medium text-white">messages</span> and <span className="font-medium text-white">code generated</span> after the selected point.
        </p>
      </div>

    case "IN_PROGRESS":
      return null;
    case "COMPLETED":
      return <div className="flex-1 min-h-0 px-4 py-6 overflow-y-auto">
        <p className="text-[16px] text-[#8A8B91] max-w-[600px] mb-4">
          Your chat, code, and workspace have been successfully rolled back to
          the selected point. All changes made after that have been erased.
        </p>
      </div>
    case "ERROR":
      return <div className="flex-1 min-h-0 px-4 py-6 overflow-y-auto">
        <p className="text-[16px] text-[#8A8B91] max-w-[600px] mb-4">
          Your attempt to roll back couldn't be completed due to a temporary connection issue or because the snapshot data is still processing.
        </p>
      </div>
    case "FAILED":
      return <div className="flex-1 min-h-0 px-4 py-6 overflow-y-auto">
        <p className="text-[16px] text-[#8A8B91] max-w-[600px] mb-4">
          We have tried multiple times to roll back to your selected point, but the operation cannot be completed. Your current session may be unusable.
        </p>
      </div>
    default:
      return null;
  }
}

const Prompt = ({ status, prompt, nextAction }: { status: ReversionStatus, prompt: string, nextAction?: string }) => {

  switch (status) {
    case "INIT":
      return (
        <div className="flex-1 min-h-0 px-6 pb-2 overflow-y-auto">
          <p className="text-[#FF884D] text-base font-medium mb-6">
            {prompt}
          </p>
        </div>
      )
    case "IN_PROGRESS":
      return null;
    case "COMPLETED":
      return (
        <div className="flex-1 min-h-0 px-6 py-2 overflow-y-auto">
          <p className="text-[#27BF7B] text-base font-medium mb-6">
            {prompt}
          </p>
        </div>
      )
    case "ERROR":
      return (<div className="flex-1 min-h-0 px-6 py-2 overflow-y-auto">
        <span className="text-[#ED5B5B] text-base font-medium">
          {`${prompt} - `}
        </span>
        {nextAction && <span className="text-base font-medium text-white">
          {nextAction}
        </span>}
      </div>
      )
    case "FAILED":
      return (
        <div className="flex-1 min-h-0 px-6 py-2 overflow-y-auto">
          <p className="mb-6 text-base font-medium text-white">
            <div className="inline-flex gap-2 items-top">
              <ServerCrash size={35} color="#ED5B5B" />
              {prompt}
            </div>
            {nextAction && <div className="mt-4 mb-8 text-base font-medium text-white">
              <div className="inline-flex gap-2 items-top">
                <Mail size={22} color="white" />
                {nextAction}
              </div>
              <span className="text-base font-medium text-green-500">&nbsp; {SUPPORT_EMAIL}</span>
            </div>}
          </p>
        </div>
      )
    default:
      return null;
  }
}



const DialogIcon = ({ currentStatus }: { currentStatus: ReversionStatus }) => {
  switch (currentStatus) {
    case "COMPLETED":
      return (
        <div className="relative">
          <Clock className="w-6 h-6 text-white" />
          <div className="absolute -bottom-1 -right-1 bg-[#29CC83] rounded-full w-4 h-4 flex items-center justify-center">
            <Check className="w-3 h-3 text-black" />
          </div>
        </div>
      )
    case "IN_PROGRESS":
      return (
        <Clock className="w-6 h-6 text-[#FF884D]" />
      )
    case "INIT":
      return (
        <Clock className="w-4 h-4 text-[#FF884D]" />
      )
    case "FAILED":
      return (
        <RevertFailedIcon size={40} color="#ED5B5B" />
      )
    case "ERROR":
      return (
        <RevertFailedIcon size={40} color="#ED5B5B" />
      )
    default:
      return null;
  }
}

const DialogIconContainer = ({ currentStatus }: { currentStatus: ReversionStatus }) => {
  return (
    <div className="absolute left-5 top-4">
      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-[#2A2A2C]">
        <DialogIcon currentStatus={currentStatus} />
      </div>
    </div>
  )
}

const reversionStatusMap: Record<ReversionStatus, ReversionStatusProps> = {
  INIT: {
    status: "INIT",
    header: <Header header="Revert to a Previous Point" />,
    description: <Description status="INIT" />,
    prompt: <Prompt status="INIT" prompt="This action cannot be undone." />,
    etaMessage: "Reverting typically takes 1–2 minutes.",
    buttons: ["cancel", "confirm"],
  },
  IN_PROGRESS: {
    status: "IN_PROGRESS",
    header: <Header header="Reverting to the previous point" />,
    description: <Description status="IN_PROGRESS" />,
    prompt: <Prompt status="IN_PROGRESS" prompt="Are you sure you want to continue?" />,
    eta: true,
    buttons: ["inProgress"]
  },
  COMPLETED: {
    status: "COMPLETED",
    header: <Header header="Reversion Complete !" />,
    description: <Description status="COMPLETED" />,
    prompt: <Prompt status="COMPLETED" prompt="To continue, send a message to our agent to restart the conversation." />,
    buttons: ["okay"]
  },
  ERROR: {
    status: "ERROR",
    header: <Header header="Reversion Failed !" />,
    description: <Description status="ERROR" />,
    prompt: <Prompt status="ERROR" prompt="Please try again" nextAction="If the problem persists after multiple attempts, a more serious issue may be at play." />,
    attempts: true,
    maxAttempts: 3,
    buttons: ["contact", "retry"]
  },
  FAILED: {
    status: "FAILED",
    header: <Header header="Revert Permanently Failed" />,
    description: <Description status="FAILED" />,
    prompt: <Prompt status="FAILED" prompt="Unfortunately, your work since the last successful snapshot may not be recoverable through normal means." nextAction="Please contact support for assistance with restoring your work" />,
    buttons: ["contact", "okay-failed"]
  }
};

export function RollbackModal({
  isOpen,
  onOpenChange,
  rollbackRequestId,
  containerId,
  jobId,
  session,
  resetPolling,
  setAiMessages
}: RollbackModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [timer, setTimer] = useState(0);
  const [currentStatus, setCurrentStatus] = useState<ReversionStatus>("INIT");
  const [rollbackType, setRollbackType] = useState<'all' | 'messages'>('all');
  
  const {
    isRollbackLoading,
    rollbackStatus,
    handleConfirmRollback,
    handleRollbackComplete,
    resetRollbackStatus,
  } = useRollback({
    containerId,
    jobId: jobId,
    session,
    resetPolling,
    rollbackRequestId,
    onRollbackSuccess: () => setAiMessages([]), // Clear messages on successful rollback
    maxAttempts: MAX_RETRY_ATTEMPTS
  });


  useEffect(() => {
    let interval: NodeJS.Timeout;
    let stepTimeout: NodeJS.Timeout;

    // Only update currentStatus if modal is open to prevent flash when closing
    if (isOpen) {
      setCurrentStatus(rollbackStatus);
    }

    if (rollbackStatus === "IN_PROGRESS") {
      // Start the timer
      interval = setInterval(() => {
        setTimer(prev => prev + 1);
      }, 1000);

      // Progress through steps
      if (currentStep < rollbackSteps.length - 1) {
        const stepDurations = [2000, 3000, 4000, 3000, 3000]; // Time each step takes
        stepTimeout = setTimeout(() => {
          setCurrentStep(prev => prev + 1);
        }, stepDurations[currentStep]);
      }
    }
    if (rollbackStatus === "COMPLETED") {
      handleRollbackComplete();
      if (isOpen) {
        setCurrentStatus("COMPLETED");
      }
    }

    if (rollbackStatus === "INIT" || rollbackStatus === "ERROR" || rollbackStatus === "FAILED") {
      setCurrentStep(0);
      setTimer(0);
    }

    return () => {
      clearInterval(interval);
      clearTimeout(stepTimeout);
    };

  }, [isRollbackLoading, rollbackStatus, currentStep, isOpen]);


  useEffect(() => {
    const retryAttempts = getRetryAttemptsFromLocalStorage(jobId || '');
    if (retryAttempts >= MAX_RETRY_ATTEMPTS) {
      setCurrentStatus("FAILED");
    } else {
      setCurrentStatus("INIT");
    }
  }, [rollbackRequestId, jobId]);

  // Reset status when modal opens
  useEffect(() => {
    if (isOpen) {
      resetRollbackStatus();
      setCurrentStatus("INIT");
      setCurrentStep(0);
      setTimer(0);
    }
  }, [isOpen, resetRollbackStatus]);


  const handleModalClose = () => {
    // Don't reset status when closing to prevent flash
    onOpenChange(false);
  }

  // Format the timer as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleModalClose}>
      {/* @ts-ignore */}
      <DialogContent hideclosebutton={currentStatus === "IN_PROGRESS" ? true : false} onInteractOutside={(e) => {
        if (currentStatus === "IN_PROGRESS") e.preventDefault();
      }} className="p-0 m-4 mx-auto max-w-[calc(100vw-32px)] md:max-w-[600px] max-h-[85vh] flex flex-col border-[#2A2A2C] bg-[#0E0E0F] overflow-hidden">
        {/* Header with icon */}
        <div className="relative border-b border-[#1A1A1C]">
          <DialogIconContainer currentStatus={currentStatus} />
          {reversionStatusMap[currentStatus].header}
        </div>
        {reversionStatusMap[currentStatus].description}
        {reversionStatusMap[currentStatus].prompt}

        {/* Init status */}
        {currentStatus === "INIT" ? (
          <div className="flex-1 min-h-0 px-6 pt-4 pb-10 overflow-y-auto">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  title='Erase all messages and generated code'
                  type="radio"
                  id="confirm-rollback-all"
                  name="rollback-confirmation"
                  className="h-4 w-4 accent-[#FF884D] border-[#FF884D] focus:ring-[#FF884D] focus:ring-offset-0 checked:bg-[#FF884D]"
                  onChange={() => setRollbackType('all')}
                  checked={rollbackType === 'all'}
                />
                <Label htmlFor="confirm-rollback-all" className="text-white">
                  Erase all messages and generated code
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  title='Erase messages only (keep code)'
                  type="radio"
                  id="confirm-rollback-messages"
                  name="rollback-confirmation"
                  className="h-4 w-4 accent-[#FF884D] border-[#FF884D] focus:ring-[#FF884D] focus:ring-offset-0 bg-transparent"
                  onChange={() => setRollbackType('messages')}
                  checked={rollbackType === 'messages'}
                />
                <Label htmlFor="confirm-rollback-messages" className="text-white">
                  Erase messages only (keep code)
                </Label>
              </div>
            </div>
          </div>
        ) : null}

        {/* Rollback Steps Content */}
        {currentStatus === "IN_PROGRESS" ? (
          <div className="flex-1 min-h-0 px-6 py-6 overflow-y-auto">
            {/* Loading State */}
            <div className="space-y-6">
              {rollbackSteps.map((step, index) => {
                // Determine step status
                let status: 'pending' | 'active' | 'completed';
                if (index < currentStep) {
                  status = 'completed';
                } else if (index === currentStep) {
                  status = 'active';
                } else {
                  status = 'pending';
                }

                return (
                  <div key={step.id} className="flex items-center">
                    <div className="w-6 h-6 mr-3">
                      {status === 'completed' ? (
                        <div className="flex items-center justify-center w-5 h-5 bg-black rounded-full">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                      ) : status === 'active' ? (
                        <div className="w-5 h-5 rounded-full bg-[#FF884D] flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        </div>
                      ) : (
                        <div className="w-5 h-5 rounded-full border border-[#2A2A2C]"></div>
                      )}
                    </div>
                    <span className={`text-base ${status === 'active' ? 'text-[#FF884D] font-medium' : 'text-[#8A8B91]'
                      }`}>
                      {step.label}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        ) : null}

        {/* Footer */}
        <div className="border-t border-[#1A1A1C] px-6 py-4">
          {/* Footer Flex Container */}
          <div className="flex items-center justify-between gap-2">
            {/* Div 1: ETA message and ETA */}
            <div className="items-center hidden flex-grow-2 md:flex">

              {reversionStatusMap[currentStatus].etaMessage ? (
                <>
                  <ClockFill size={32} color="#7A7A7B"  />
                  <div className="text-[#8A8B91] text-sm hidden md:block">{reversionStatusMap[currentStatus].etaMessage}</div>
                </>
              ) : null}

              {reversionStatusMap[currentStatus].eta ? (
                <>
                  <ClockFill size={32} color="#7A7A7B" />
                  <div className="text-[#8A8B91] text-lg">{formatTime(timer)}</div>
                </>
              ) : null}


            </div>

            {/* Div 2: Buttons */}
            <div className="flex items-center gap-2 mt-4">
              {reversionStatusMap[currentStatus].buttons.map((button, index) => {
                switch (button) {
                  case "cancel":
                    return (
                      <Button key={index} variant="secondary" onClick={() => handleModalClose()} className="text-[#DDDDE6] bg-[#2A2A2C] hover:bg-[#3A3A3C] rounded-full h-10 px-6">
                        Cancel
                      </Button>
                    )
                  case "confirm":
                    return (
                      <Button key={index} onClick={() => {
                        handleConfirmRollback(rollbackType);
                        setTimer(0);
                        setCurrentStep(0);
                      }} className="bg-[#FF884D] hover:bg-[#E57440] text-background rounded-full h-10 px-6 flex items-center">
                        Confirm Revert
                        <Clock className="w-5 h-5 ml-2" />
                      </Button>
                    )
                  case "inProgress":
                    return (
                      <Button key={index} variant="secondary" onClick={() => { }} className="text-[#DDDDE6] bg-[#2A2A2C] hover:bg-[#3A3A3C] rounded-full h-10 px-6">
                        <span className="font-medium text-[#FF884D]">Reverting...</span>
                        <div className="ml-2 w-5 h-5 rounded-full border-2 border-[#FF884D] border-t-transparent animate-spin"></div>
                      </Button>
                    )
                  case "okay":
                    return (
                      <Button key={index} onClick={() => {
                        handleModalClose();
                      }} className="flex items-center h-10 px-6 text-black bg-white rounded-full hover:bg-white/80">
                        Okay, Got It
                      </Button>
                    )
                  case "okay-failed":
                    return (
                      <Button key={index} onClick={() => handleModalClose()} className="flex items-center h-10 px-6 text-black bg-white rounded-full hover:bg-white/80">
                        Okay, Got It
                      </Button>
                    )
                  case "retry":
                    return (
                      <Button key={index} onClick={() => handleConfirmRollback(rollbackType)} className="flex items-center h-10 px-6 bg-white rounded-full hover:bg-white/80 text-background">
                        Retry
                        <Undo2 className="w-5 h-5" />
                      </Button>
                    )
                  case "contact":
                    return (
                      <Button key={index} onClick={() => window.location.href = `mailto:${SUPPORT_EMAIL}`} className="bg-[#272729] hover:bg-[#3A3A3C] text-white rounded-full h-10 px-6 flex items-center">
                        Contact Support
                        <Mail className="w-5 h-5 ml-2" />
                      </Button>
                    )
                  case "back":
                    return (
                      <Button
                        variant="secondary"
                        onClick={() => handleModalClose()}
                        className="text-[#DDDDE6] bg-[#2A2A2C] hover:bg-[#3A3A3C] rounded-full h-10 px-6 flex items-center gap-2"
                      >
                        <Undo className="w-5 h-5" />

                        Go Back
                      </Button>
                    )
                  default:
                    return null;
                }
              })}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
