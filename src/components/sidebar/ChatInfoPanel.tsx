import { Button, buttonVariants } from "@/components/ui/button";
import { X } from "lucide-react";
import CopyButton from "../CopyButton";
import { cn } from "@/lib/utils";
import LinkSVG from "@/assets/Link.svg";
import CopyableSVG from "@/assets/Copyable.svg";
import Github from "@/assets/menu-icons/github.svg";
import BranchSVG from "@/assets/branch2.svg";
import GreenBranch from "@/assets/GreenBranch.svg";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { useDeploy } from "@/hooks/useDeploy";

interface ChatInfoPanelProps {
  isOpen: boolean;
  onClose: () => void;
  chatInfo: {
    modelName?: string;
    agentName?: string;
    containerStatus?: string;
    imageVersion?: string;
    containerName?: string;
    containerId?: string;
    assignedPorts?: {
      service: string;
      hostPort: number;
      containerPort: number;
    }[];
    jobId?: string;
    isCloudFlow?: boolean;
    promptName?: string;
    promptVersion?: string;
    costLimit?: number;
    vscodeUrl?: string;
    vscodePassword?: string;
    targetRepo?: {
      branch: string;
      repo: string;
      owner: string;
      provider: string;
    } | null;
    sourceRepo?: {
      branch: string;
      repo: string;
      owner: string;
      provider: string;
    } | null;
  };
}

interface InfoItemProps {
  title: string;
  value?: string;
  copyable?: boolean;
  maxChars?: number;
  onClick?: () => void;
  className?: string;
  icon?: string;
}

const InfoSection = ({
  title,
  value,
  copyable = false,
  maxChars,
  onClick,
  className,
  icon,
}: {
  title: string;
  value?: string;
  copyable?: boolean;
  maxChars?: number;
  onClick?: () => void;
  className?: string;
  icon?: string;
}) => {
  const displayValue =
    maxChars && value
      ? value.length > maxChars
        ? `${value.slice(0, maxChars)}...`
        : value
      : value;


  return (
    <div className="space-y-2">
      <div className="text-[#7B7B80] font-medium font-['Inter'] text-sm">
        {title}
      </div>
      <div
        className={cn("flex items-center justify-between gap-2", className)}
        onClick={onClick}
      >
        <div className="text-[#C4C4CC] font-['Inter'] text-base group-hover:text-[#2EBBE5] transition-colors duration-200">
          {displayValue || "—"}
        </div>
        {copyable && value && (

        <CopyButton
          tooltipEnabled={true}
          showTooltipOnHover={true}
          tooltipText="Copy"
          copiedTooltipText="Copied"
          showIcon={true}
         iconOnly={true}
          className="border-none bg-none"
         buttonClassName={buttonVariants({ variant: "outline", size: "sm" , className: "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 border-white/20 border hover:bg-white/20 hover:text-white text-white/40"})}
          value={()=>   value}
          iconProps={{ size: 16 }}
          onCopy={async () => { await navigator.clipboard.writeText(value);}}
      />
        )}

        {icon && !copyable && (
          <div
            className="flex items-center justify-center min-w-[40px] min-h-[36px] cursor-pointer rounded-md hover:bg-white/10 transition-colors duration-200"
            onClick={onClick}
          >
            <img src={icon} alt="icon" className="w-4 h-4" />
          </div>
        )}
      </div>
    </div>
  );
};

export function ChatInfoPanel({
  isOpen,
  onClose,
  chatInfo,
}: ChatInfoPanelProps) {
  const isEmergentUser = useIsEmergentUser();
  const { deployStatus, deployUrl, customDomainUrl, customDomain } = useDeploy(chatInfo.jobId);

  return (
    <div
      className={cn("md:top-14 max-md:inset-0 h-[calc(100%-4rem)] w-screen md:w-full bg-[#0F0F10]  transform transition-transform duration-300 ease-in-out z-50 pointer-events-auto max-md:absolute", isOpen ? "block":"hidden")}
    >
      <div className="flex flex-col h-full">
        <div className="p-4 md:px-6 md:py-5 bg-[#181818] md:bg-transparent flex items-center justify-between border-b border border-[#242424]/60">
          <div className="text-[#939399] font-['Brockmann']  text-[15px]  md:text-[18px] font-medium leading-[24px]">
            Run Details
          </div>

          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="w-8 h-8 hover:bg-transparent"
          >
            <X className="w-6 h-6 text-white" />
          </Button>
        </div>

        <div className="flex-1 p-6 space-y-8 overflow-x-hidden overflow-y-auto">
          <div className="space-y-6">
            <div className="space-y-4">
              <InfoSection title="Model" value={chatInfo.modelName} />
              {isEmergentUser && <InfoSection title="Agent" value={`${chatInfo.promptName}`} />}
            </div>

            <div className="space-y-4">
              <InfoSection
                title="Job ID"
                value={chatInfo.jobId}
                copyable
                icon={CopyableSVG}
              />
            </div>

           {deployStatus === "success" &&   <InfoBar title="DEPLOYMENT INFO" />}

           {deployStatus === "success" && <div className="text-[#C4C4CC] font-['Inter'] text-base space-y-4 flex-col flex">
                <InfoSection
                  title="Live Link"
                  icon={LinkSVG}
                  value={deployUrl}
                  onClick={() => {
                    if (deployUrl) {
                      window.open(deployUrl, "_blank");
                    }
                  }}
                  className="cursor-pointer group"
                />
                {customDomainUrl && customDomain?.status === "verified" && (
                  <InfoSection
                    title="Custom Domain"
                    icon={LinkSVG}
                    value={customDomain.domain}
                    onClick={() => {
                      if (customDomainUrl) {
                        window.open(customDomainUrl, "_blank");
                      }
                    }}
                    className="cursor-pointer group"
                  />
                )}
              </div>}

          {chatInfo.vscodeUrl &&   <InfoBar title="VS CODE INFO" />}

            <div className="space-y-4">
              <div className="text-[#C4C4CC] font-['Inter'] text-base space-y-4 flex-col flex">
                <InfoSection
                  title="VSCode URL"
                  icon={LinkSVG}
                  value={`${chatInfo.vscodeUrl}`}
                  onClick={() => {
                    window.open(chatInfo.vscodeUrl, "_blank");
                  }}
                  className="cursor-pointer group"
                />
                <InfoSection
                  title="VSCode Password"
                  icon={CopyableSVG}
                  value={`${chatInfo.vscodePassword}`}
                  copyable
                />
              </div>
            </div>

           {(chatInfo.sourceRepo || chatInfo.targetRepo) &&   <InfoBar title="GITHUB INFO" />}



           {chatInfo.sourceRepo && (
              <div
                className="bg-[#FFFFFF0D] p-3 rounded-[8px] text-sm flex flex-col gap-2 font-['Inter'] font-medium cursor-pointer hover:opacity-80 transition-opacity duration-200"
                onClick={() => {
                  const repoUrl = `https://github.com/${chatInfo.sourceRepo?.owner}/${chatInfo.sourceRepo?.repo}/tree/${chatInfo.sourceRepo?.branch}`;
                  window.open(repoUrl, "_blank");
                }}
              >
                <span className="text-[#7B7B80] font-['Inter'] text-sm font-medium">Source Repository</span>

                <div className="flex items-center gap-1">
                  <img src={Github} alt="Github" className="w-5 h-5" />
                  <span className="capitalize text-[#C4C4CC] text-nowrap truncate">
                    {chatInfo.sourceRepo?.owner}{" "}/{" "}{chatInfo.sourceRepo?.repo}
                  </span>
                  <div className="flex items-center gap-1 ml-2 text-nowrap">
                    <img src={BranchSVG} alt="Branch"  />
                    <span className="text-[#DADEE5] text-nowrap truncate">{chatInfo.sourceRepo?.branch}</span>
                  </div>
                </div>
              </div>
            )}


            {chatInfo.targetRepo && (
              <div
                className="bg-[#FFFFFF0D] p-3 rounded-[8px] text-sm flex flex-col gap-2 font-['Inter'] font-medium cursor-pointer hover:opacity-80 transition-opacity duration-200"
                onClick={() => {
                  const repoUrl = `https://github.com/${chatInfo.targetRepo?.owner}/${chatInfo.targetRepo?.repo}/tree/${chatInfo.targetRepo?.branch}`;
                  window.open(repoUrl, "_blank");
                }}
              >
                <span className="text-[#7B7B80] font-['Inter'] text-sm font-medium">Most Recent Export</span>

                <div className="flex items-center gap-1">
                  <img src={Github} alt="Github" className="w-5 h-5" />
                  <span className="capitalize text-[#C4C4CC]">
                    {chatInfo.targetRepo?.owner}{" "}/{" "}{chatInfo.targetRepo?.repo}
                  </span>
                  <div className="flex items-center gap-1 ml-2">
                    <img src={GreenBranch} alt="Branch" />
                    <span className="text-[#2EE572]">{chatInfo.targetRepo?.branch}</span>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  );
}

const InfoBar = ({ title }: { title: string }) => {
  return (
    <>
      <div className="flex items-center w-full gap-2">
        <div className="h-[1px] flex-1 bg-[#FFFFFF12]"></div>
        <span className="text-[#FFFFFF4D] text-[14px] font-berkeley uppercase tracking-[1px]">
          {title}
        </span>
        <div className="h-[1px] max-w-[30px] flex-1 bg-[#FFFFFF12]"></div>
      </div>
    </>
  );
};
