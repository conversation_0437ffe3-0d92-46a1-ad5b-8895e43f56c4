import React, { useState, useEffect } from "react";

import {
  ChevronDown
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import LockedWeb from "@/assets/LockedWeb.svg";
import LockedWebBlue from "@/assets/LockedWebBlue.svg";
// Import link icon
import LinkedIcon from "@/assets/line-md_link.svg";
import UnlinkIcon from "@/assets/Unlink.svg";
import PulseDot from "../PulseDot";

import CopyIconSVG from "@/assets/copyPurple.svg";
import RefreshIconSVG from "@/assets/flowbite_refresh-outline.svg";
import CloseIcon from "@/assets/close.svg"
import { URL_LINKS } from "@/constants/constants";
interface CustomDomainSectionProps {
  jobId?: string;
  deployStatus: string;
  onUnlinkDomain?: (domain: string) => void;
  customDomain?: {
    domain: string;
    status: "verified" | "pending" | "failed";
    dns_records: {
      type: string;
      name: string;
      value: string | undefined;
    }[];
  } | null;
  loading?: boolean;
  registerDomain: (domain: string, jobId: string) => Promise<any>;
  verifyDomain: (domain: string, jobId: string) => Promise<any>;
  connectDomain: (domain: string) => Promise<any>;
  checkDeployStatus: (jobId: string, silent: boolean) => Promise<any>;
}

const CustomDomainSection: React.FC<CustomDomainSectionProps> = ({
  jobId,
  deployStatus,
  onUnlinkDomain,
  checkDeployStatus,
  customDomain,
  loading,
  registerDomain,
  verifyDomain,
  connectDomain,
}) => {
  const { toast } = useToast();
  const [domain, setDomain] = useState<string>("");
  const [step, setStep] = useState<"register" | "verify" | "connect">("register");

  // Validate domain format
  const isValidDomain = (domain: string): boolean => {
    const domainRegex =
      /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;
    return domainRegex.test(domain);
  };

  // Handle domain registration
  const handleRegisterDomain = async () => {
    if (!domain || !isValidDomain(domain)) {
      toast({
        title: "Invalid Domain",
        description: "Please enter a valid domain name",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    try {
      const response = await registerDomain(domain, jobId || "");
      console.log("xoxoxo registerDomain response:", response);
      if (response) {
        setStep("verify");
        // Automatically show DNS record details
        setShowDomainForm(false);
        setShowDomainDetails(true);

        toast({
          title: "Domain Registered",
          description: response.message || "Please add the DNS records to verify your domain",
          duration: 3000,
        });
      } else {
        toast({
          title: "Registration Failed",
          description: "Failed to register domain",
          variant: "destructive",
          duration: 3000,
        });
      }
    } catch (error: any) {
      toast({
        title: "Registration Failed",
        description: error.message || "Failed to register domain",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Handle domain verification
  const handleVerifyDomain = async () => {
    if (!domain) return;

    try {
      const response = await verifyDomain(domain, jobId || "");
      if (!response) return;

      if (response.status === "pending_verification") {
        toast({
          title: "Verification Pending",
          description:
            "DNS records not fully propagated yet. Please try again in a few minutes.",
          variant: "default",
          duration: 3000,
        });
      } else if (
        response.status === "connected" ||
        response.status === "not_connected"
      ) {
        setStep("connect");
        toast({
          title: "Domain Verified",
          description: "Your domain has been verified successfully",
          duration: 3000,
        });

        // If verification was successful (verified: true), call checkDeployStatus to update deployment status
        if (response.verified === true && jobId) {
          //console.log("[CustomDomainSection] Domain verification successful, updating deployment status");
          try {
            // Use the checkDeployStatus function from useDeploy to update the deployment status
            await checkDeployStatus(jobId, false);
          } catch (error) {
            console.error("[CustomDomainSection] Error updating deployment status after domain verification:", error);
          }
        }
      }
    } catch (error: any) {
      toast({
        title: "Verification Failed",
        description: error.message || "Failed to verify domain",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Handle connecting domain to app
  const handleConnectDomain = async () => {
    if (!domain || !jobId) return;

    try {
      const response = await connectDomain(domain);
      if (!response) return;

      if (response.status === "connected") {
        toast({
          title: "Domain Connected",
          description: `Your app is now accessible at ${domain}`,
          duration: 3000,
        });
      }
    } catch (error: any) {
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect domain to app",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Check domain status
  const checkDomainStatus = async () => {
    if (!domain) return;

    try {
      const response = await verifyDomain(domain, jobId || "");
      toast({
        title: "Status Updated",
        description: "Domain status has been refreshed",
        duration: 1500,
      });

      // If verification was successful (verified: true), call checkDeployStatus to update deployment status
      if (response && response.verified === true && jobId) {
        //console.log("[CustomDomainSection] Domain verification successful during status check, updating deployment status");
        try {
          // Use the checkDeployStatus function from useDeploy to update the deployment status
          await checkDeployStatus(jobId, false);
        } catch (error) {
          console.error("[CustomDomainSection] Error updating deployment status after domain verification:", error);
        }
      }
    } catch (error: any) {
      toast({
        title: "Status Check Failed",
        description: error.message || "Failed to check domain status",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Handle domain unlinking
  const handleUnlinkDomain = () => {
    if (onUnlinkDomain && customDomain?.domain) {
      // Call the parent component's handler to open the modal
      onUnlinkDomain(customDomain.domain);
    }
  };



  // Copy DNS record to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to Clipboard",
      description: "DNS record copied to clipboard",
      duration: 1500,
    });
  };

  const formatHostname = (_name: string, domain: string): string => {
     domain = domain.replace(/^(https?:\/\/)?(www\.)?/i, "");

  const parts = domain.split(".");

  if (parts.length <= 2) {
    return "@";
  }

  const specialTLDs = [
    "co.uk", "com.au", "co.nz", "co.jp", "or.jp", "co.kr",
    "com.br", "com.mx", "co.za", "co.in", "com.sg"
  ];

  const lastTwoParts = parts.slice(-2).join(".");
  if (specialTLDs.includes(lastTwoParts) && parts.length > 2) {
    return parts.slice(0, -2).join(".");
  }

  return parts[0];
  };



  // Effect to update the domain input when customDomain changes
  useEffect(() => {
    if (customDomain && customDomain.domain) {
      setDomain(customDomain.domain);

      // Set the step based on the domain status
      if (customDomain.status === 'verified') {
        setStep('connect');
      } else if (customDomain.status === 'pending') {
        setStep('verify');
      } else {
        setStep('register');
      }

      // Automatically show DNS record details after registration
      setShowDomainForm(false);
      setShowDomainDetails(true);
    } else {
      // Reset to initial state if customDomain is null or has empty domain
      setStep('register');
      setShowDomainForm(false);
      setShowDomainDetails(false);
    }
  }, [customDomain]);

  // Only show this section if deployment is successful
  if (deployStatus !== "success") {
    return null;
  }

  const [showDomainDetails, setShowDomainDetails] = useState(false);
  const [showDomainForm, setShowDomainForm] = useState(false);

  // Determine the domain status display
  const getDomainStatusDisplay = () => {
    if (!customDomain || !customDomain.domain || !customDomain.status) return null;

    let statusColor = "";
    let statusText = "";

    switch (customDomain.status) {
      case "verified":
        statusColor = "#4CAF50"; // Green
        statusText = "Verified";
        break;
      case "pending":
        statusColor = "#E38F45"; // Orange
        statusText = "Pending";
        break;
      case "failed":
        statusColor = "#F44336"; // Red
        statusText = "Failed";
        break;
      default:
        statusColor = "#9E9E9E"; // Gray
        statusText = "Unknown";
    }

    return (
      <div className="flex items-center gap-2">
        <PulseDot
          color={statusColor}
          size={16}
          innerSize={8}
          animate={customDomain.status === "pending"}
          className=""
        />
        <span style={{ color: statusColor }} className="text-[14px]">{statusText}</span>
      </div>
    );
  };

  // Handle domain input change
  const handleDomainChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDomain(e.target.value);
  };

  // Handle domain form submission
  const handleDomainSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (step === "register") {
      handleRegisterDomain();
    } else if (step === "verify") {
      handleVerifyDomain();
    } else if (step === "connect") {
      handleConnectDomain();
    }
  };

  return (
    <div className={cn("w-full p-3 md:p-6 bg-[#FFFFFF0A] items-start mt-5 rounded-[12px]", showDomainForm && "bg-[#4d9fff08] border border-[#4d9fff50]")}>
      <div className="flex items-start justify-between gap-8">
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <img src={showDomainForm ? LockedWebBlue : LockedWeb} alt="Locked Web" className="w-5 h-5 md:w-6 md:h-6" />
            <span className={cn("font-['Inter'] text-[14px] md:text-base font-medium text-nowrap", showDomainForm && "text-[#4d9fff] ")}>Custom Domain</span>
          </div>
          {!showDomainForm && <p className="text-[#FFFFFF]/60 font-['Inter'] hidden md:block">

            {customDomain?.dns_records && customDomain.dns_records.length >0 ?<>
            <span>Add the following records to your domain’s DNS settings. <a href={URL_LINKS.howToAddDomains} target="_blank" rel="noopener noreferrer" className="text-[#2087FF] font-['Inter'] font-medium underline">How to add domains?</a>
               {/* <span className="text-[#4d9fff]">Learn more</span> */}
               </span></> :"Link your personalized domain that you would like to connect to this app."}
          </p>}

        </div>


        {customDomain ? (
          <button
            type="button"
            onClick={() => showDomainForm ? setShowDomainForm(false) : handleUnlinkDomain()}
            className="bg-[#FFFFFF08] hover:bg-[#FFFFFF10] md:p-[16px] md:py-3 p-3 rounded-[12px] flex justify-between gap-2 items-center"
          >
            <span className="text-nowrap text-[12px] md:text-[16px]">
              {showDomainForm ? "Cancel" : "Unlink Domain"}
            </span>
            <img src={showDomainForm ? CloseIcon : UnlinkIcon} alt="Linked" className="w-6 h-6 mr-[20px]" />
          </button>
        ) : (
          <button
            type="button"
            onClick={() => setShowDomainForm(!showDomainForm)}
            className="bg-[#FFFFFF08] hover:bg-[#FFFFFF10] md:p-[16px] p-3 md:py-3 rounded-[12px] flex justify-between gap-2 items-center"
          >
            <span className="text-nowrap text-[12px] md:text-[16px]">
              {showDomainForm ? "Cancel" : "Link Domain"}
            </span>
          {showDomainForm ? (
               <img src={CloseIcon} alt="Linked" className="w-6 h-6" />
            ) : (
                <img src={LinkedIcon} alt="Linked" className="w-6 h-6 mr-[20px]" />
            )}
          </button>
        )}
      </div>

           {!showDomainForm && <p className="text-[#FFFFFF]/60 font-['Inter'] text-[13px] md:hidden mt-2">

            {customDomain?.dns_records && customDomain.dns_records.length >0 ?<>
            <span>Set up your personalized domain. Add the following records to your DNS provider to verify ownership.
               {/* <span className="text-[#4d9fff]">Learn more</span> */}
               </span></> :"Link your personalized domain that you would like to connect to this app."}
          </p>}

      {showDomainForm && !customDomain ? (
        <div className="mt-8">
          <form onSubmit={handleDomainSubmit} className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <label htmlFor="domain-input" className="text-[#99c8ff]/80 font-['Inter'] text-[14px]">Enter your domain</label>
              <input
                id="domain-input"
                type="text"
                autoFocus
                value={domain}
                onChange={handleDomainChange}
                placeholder="example.com"
                className="w-full p-3 bg-[#66adff08] px-4 font-['Inter'] font-medium rounded-[12px] text-[#CCE4FF] placeholder:text-[#4d9fff80] border border-[#66adff40] text-[16px] focus:outline-none focus:border focus:border-[#66adff60]"
              />
            </div>

            <div className="flex justify-end gap-2">
              <button
                type="submit"
                disabled={loading || !domain || !isValidDomain(domain)}
                className={` w-full rounded-[12px] p-3 font-semibold ${loading || !domain || !isValidDomain(domain) ? 'bg-[#0070F350] text-[#FFFFFF4D] cursor-not-allowed' : 'bg-[#0070F3] text-white'}`}
              >
                {loading ? 'Processing...' : 'Next'}
              </button>
            </div>
          </form>
        </div>
      ) : customDomain ? (
        <div className="bg-[#0076FF05] rounded-[12px] border-[#66acff20] overflow-clip border mt-6">
          <div onClick={() => setShowDomainDetails(!showDomainDetails)} className={cn("flex items-center cursor-pointer justify-between px-5 py-4  ", showDomainDetails && "bg-[#0076FF10] border-b border-b-[#66acff20]")}>
            <div className="flex items-center gap-4">
              <span className="text-[#CCE4FF] text-[14px] md:text-[16px] font-medium font-berkeley">
                {customDomain.domain}
              </span>
              {getDomainStatusDisplay()}
            </div>

            <div className="flex items-center gap-4">
              <div
                className="cursor-pointer"
                onClick={() => copyToClipboard(customDomain.domain)}
              >
                <img src={CopyIconSVG} alt="Copy" className="w-6 h-6" />
              </div>
              <div
                className="cursor-pointer"
                onClick={() => checkDomainStatus()}
              >
                <img
                  src={RefreshIconSVG}
                  alt="Refresh"
                  className={`w-6 h-6 opacity-[50%] ${loading ? 'animate-spin' : ''}`}
                />
              </div>
              <div
                className="cursor-pointer"
                onClick={() => setShowDomainDetails(!showDomainDetails)}
              >
                <ChevronDown className={cn("w-6 h-6", { "rotate-180": showDomainDetails })} />
              </div>
            </div>
          </div>

          {(showDomainDetails) && customDomain.dns_records && customDomain.dns_records.length > 0 && (
            <div className="flex flex-col gap-5 p-2 md:p-4">
              {customDomain.dns_records.filter(record => record && record.type && record.name && record.value).map((record, index) => (
                <div key={index} className="flex gap-1 md:gap-4 ">
                  <div className="flex flex-col items-start gap-1 md:gap-2 w-[20%]">
                    <span className="text-[#99c8ff]/80 font-['Inter'] text-[14px]">Type</span>
                    <input
                      readOnly
                      aria-label="DNS Record Type"
                      type="text"
                      value={record.type || ""}
                       className="w-full p-3 bg-[#66adff10] px-4 font-['Inter'] font-medium rounded-[12px] text-[#CCE4FF] placeholder:text-[#4d9fff80] text-[14px] md:text-[16px] focus:outline-none "
                    />
                  </div>
                  <div className="flex flex-col items-start gap-1 md:gap-2 w-[35%]">
                    <span className="text-[#99c8ff]/80 font-['Inter'] text-[14px]">Host-Name</span>
                    <div className="relative w-full">
                      <input
                      readOnly
                      aria-label="DNS Record Name"
                      type="text"
                      value={formatHostname(record.name || "", customDomain?.domain || "")}
                      className="w-full p-3 bg-[#66adff10] px-4 font-['Inter'] font-medium rounded-[12px] text-[#CCE4FF] placeholder:text-[#4d9fff80] text-[14px]  md:text-[16px] focus:outline-none "
                    />
                    <div
                        className="absolute transform -translate-y-1/2 cursor-pointer right-3 top-1/2"
                        onClick={() => record.name && copyToClipboard(formatHostname(record.name, customDomain?.domain || ""))}
                      >
                        <img src={CopyIconSVG} alt="Copy" className="w-5 h-5" />
                      </div>
                      </div>
                  </div>
                  <div className="flex flex-col items-start gap-1 md:gap-2 w-[45%]">
                    <span className="text-[#99c8ff]/80 font-['Inter'] text-[14px]">Value</span>
                    <div className="relative w-full">
                      <input
                        readOnly
                        aria-label="DNS Record Value"
                        type="text"
                        value={record.value || ""}
                        className="w-full p-3 bg-[#66adff10] px-4 font-['Inter'] font-medium rounded-[12px] text-[#CCE4FF] placeholder:text-[#4d9fff80] text-[14px]  md:text-[16px] focus:outline-none"
                      />
                      <div
                        className="absolute transform -translate-y-1/2 cursor-pointer right-3 top-1/2"
                        onClick={() => record.value && copyToClipboard(record.value)}
                      >
                        <img src={CopyIconSVG} alt="Copy" className="w-5 h-5" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => checkDomainStatus()}
                className="flex cursor-pointer items-center justify-center text-[#FFFFFF] px-5 py-3 bg-[#FFFFFF10] hover:bg-[#FFFFFF15] rounded-[12px] gap-2"
              >
                <span>Check Status</span>
                <img
                  src={RefreshIconSVG}
                  alt="Refresh"
                  className={`w-6 h-6 ${loading ? 'animate-spin' : ''}`}
                />
              </button>
            </div>
          )}
        </div>
      ) : null}
    </div>
  );
};

export default CustomDomainSection;
