import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import PulseDot from "../PulseDot";
import { DeploymentHistoryRun } from "@/services/agentApi";
import TimeReverSVG from "@/assets/TimeRever.svg";
import ArrowDownBig from "@/assets/ArrowDownBig.svg";
import Logs from "@/assets/deployment/Logs.svg";
import LogWithError from "@/assets/deployment/LogWithError.svg";
import WebDark from "@/assets/deployment/WebDarkLink.svg";

interface DeploymentBarProps {
  deployStatus: string;
  currentActive: boolean;
  showLogs?: boolean;
  deployTime: string;
  nextExist: boolean;
  deploymentLink?: string;
  deploymentRun?: DeploymentHistoryRun;
  handleDeploy?: (jobId: string, image: string) => void;
  jobId?: string;
  onRollbackClick?: (id: string) => void;
  inRollbackModal?: boolean;
  isRollbackTarget?: boolean;
  latestRunStatus?: "running" | "success" | "failed" | "pending" | null;
  handleLogsClick?: () => void;
}

const DeploymentBar = ({
  deployStatus,
  showLogs = false,
  currentActive,
  deployTime,
  deploymentLink,
  deploymentRun,
  onRollbackClick,
  inRollbackModal = false,
  isRollbackTarget = false,
  latestRunStatus = null,
  nextExist,
  handleLogsClick,
}: DeploymentBarProps) => {
  const shortId = deploymentRun?.id
    ? deploymentRun.id.substring(0, 7)
    : "unknown";

  // State to track hover on the deployment bar
  const [isHovered, setIsHovered] = React.useState(false);

  // Define animation variants
  const barVariants = {
    initial: {
      opacity: 0,
      y: 10,
      scale: 0.98,
    },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 30,
        mass: 1,
      },
    },
  };

  const buttonVariants = {
    initial: { opacity: 0, scale: 0.9 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 500,
        damping: 30,
      },
    },
    hover: {
      scale: 1.05,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 20,
      },
    },
    tap: { scale: 0.95 },
  };

  // Variants for the rollback button that appears on hover
  const rollbackButtonVariants = {
    initial: { opacity: 0, scale: 0.9, x: 10 },
    animate: {
      opacity: 1,
      scale: 1,
      x: 0,
      transition: {
        type: "spring" as const,
        stiffness: 500,
        damping: 30,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      x: 10,
      transition: {
        duration: 0.2,
      },
    },
  };

  const iconVariants = {
    initial: { rotate: 0 },
    hover: {
      rotate: 10,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 10,
      },
    },
  };

  return (
    <>
      <motion.div
        className={cn(
          "rounded-[12px] z-[100] relative flex items-center w-full justify-between",
          currentActive && !inRollbackModal
            ? "p-3 md:p-5 md:py-8 bg-gradient-to-r from-[#80FFF9]/10 to-[#1588FC]/10 md:max-h-[88px] border-[#80FFF9]/20 border-[1px]"
            : inRollbackModal && !isRollbackTarget
            ? " p-3 md:p-5 md:py-6 bg-gradient-to-r from-[#80FFF9]/10 to-[#1588FC]/10 md:max-h-[80px] border-[#80FFF9]/50 border-[1px]"
            : isRollbackTarget
            ? "p-3 md:p-5 md:py-4 bg-[#61616114] md:max-h-[60px]"
            : "md:rounded-full pr-3 pl-5 py-4 bg-[#61616114] md:max-h-[56px]"
        )}
        variants={barVariants}
        initial="initial"
        animate="animate"
        layout
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {nextExist && (
          <div className="absolute w-[1.5px] hidden md:block md:h-[26px] dotted-spaced border border-[#FFFFFF20] border-dashed -bottom-[32px] left-[28px]"></div>
        )}
        <div className="flex flex-col items-start justify-between w-full gap-3 md:gap-0 md:items-center md:flex-row">
          <div
            className={cn(
              "flex md:flex-row  items-center gap-2",
              (currentActive && !inRollbackModal) ||
                (inRollbackModal && !isRollbackTarget)
                ? "text-[#80FFF9]"
                : "text-white"
            )}
          >
            <div className="flex items-center gap-3">
              <PulseDot
                color={
                  isRollbackTarget
                    ? "#737780" // Gray for rollback target
                    : inRollbackModal && !isRollbackTarget
                    ? "#2EE572" // Green for current in rollback modal
                    : currentActive
                    ? "#2EE572"
                    : deployStatus === "failed"
                    ? "#ED5B5B"
                    : "#FFFFFF"
                }
                size={16}
                innerSize={8}
                animate={inRollbackModal && !isRollbackTarget} // Only animate for current in rollback modal
                className=""
              />
              <span
                className={cn(
                  "font-medium font-['Inter']",
                  deployStatus === "failed" && "text-[#ED5B5B]",
                  currentActive && "text-[#2EE572]"
                )}
              >
                {deployStatus === "failed"
                  ? "Failed to Deploy"
                  : currentActive
                  ? "Live"
                  : "Deployed"}
              </span>
            </div>
            <span className="font-medium opacity-50 font-berkeley">
              {shortId}
            </span>
            <div
              className={cn(
                "h-[20px] w-[2px] ",
                currentActive ? "bg-[#80FFF920]" : "bg-[#FFFFFF20]"
              )}
            ></div>
            <span className="font-medium opacity-50 font-['Inter'] text-sm text-nowrap">
              {deployTime}
            </span>
          </div>

          {/* Only show action buttons if not in rollback modal */}
          {!inRollbackModal && (
            <div className="flex items-center gap-4 group">
              {
                <>
                  {showLogs && deployStatus === "failed" && (
                    <motion.div
                      onClick={() => {
                        handleLogsClick && handleLogsClick();
                      }}
                      className="flex items-center gap-1 max-h-[40px] hover:bg-[#FFFFFF20] px-3 py-2 rounded-full cursor-pointer bg-[#FFFFFF14] "
                    >
                      <img
                        src={deployStatus === "failed" ? LogWithError : Logs}
                        alt="Logs"
                        className="w-6 h-6"
                      />
                      <span className="font-semibold ">View Logs</span>
                    </motion.div>
                  )}
                  {deployStatus === "success" && currentActive && (
                    <motion.div
                      onClick={() => {
                        if (deploymentLink) {
                          window.open(deploymentLink, "_blank");
                        }
                      }}
                      className="flex items-center gap-8 max-h-[40px] rounded-full cursor-pointer bg-[#fff] pl-[14px] pr-[10px] py-[10px]"
                      variants={buttonVariants}
                      initial="initial"
                      animate="animate"
                      whileHover="hover"
                      whileTap="tap"
                    >
                      <span className="text-[#000] font-semibold">Visit</span>

                      <motion.img
                        src={WebDark}
                        alt="Web"
                        className="w-6 h-6 grayscale-[100]"
                      />
                    </motion.div>
                  )}
                </>
              }

              {deployStatus === "success" &&
                !currentActive &&
                latestRunStatus !== "running" && (
                  <AnimatePresence>
                    {isHovered && (
                      <>
                        <motion.div
                          onClick={() =>
                            onRollbackClick &&
                            onRollbackClick(deploymentRun?.id || "")
                          }
                          className="flex cursor-pointer items-center gap-4 rounded-full bg-[#FFFFFF08] hover:bg-[#ffffff10] pl-[14px] pr-[10px] py-[6px]"
                          variants={rollbackButtonVariants}
                          initial="initial"
                          animate="animate"
                          exit="exit"
                          whileHover="hover"
                          whileTap="tap"
                        >
                          <span className="text-[#FFFFFF] font-medium">
                            Rollback
                          </span>
                          <motion.img
                            src={TimeReverSVG}
                            alt="Web"
                            className="w-6 h-6"
                          />
                        </motion.div>
                      </>
                    )}

                    <motion.div
                      onClick={() =>
                        onRollbackClick &&
                        onRollbackClick(deploymentRun?.id || "")
                      }
                      className="flex md:hidden cursor-pointer items-center gap-4 rounded-full bg-[#FFFFFF08] hover:bg-[#ffffff10] pl-[14px] pr-[10px] py-[6px]"
                      variants={rollbackButtonVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      whileHover="hover"
                      whileTap="tap"
                    >
                      <span className="text-[#FFFFFF] font-medium">
                        Rollback
                      </span>
                      <motion.img
                        src={TimeReverSVG}
                        alt="Web"
                        className="w-6 h-6"
                        variants={iconVariants}
                        initial="initial"
                        whileHover="hover"
                      />
                    </motion.div>
                  </AnimatePresence>
                )}
            </div>
          )}

          {!isRollbackTarget && inRollbackModal && (
            <div className="flex cursor-pointer items-center gap-3 px-3 py-2 rounded-full bg-[#2EE5721A]">
              <PulseDot
                color="#2EE572"
                size={16}
                innerSize={8}
                animate={false}
                className=""
              />
              <span className="text-[#2EE572] font-semibold">Current</span>
            </div>
          )}

          {isRollbackTarget && (
            <img
              src={ArrowDownBig}
              alt="Arrow"
              className="absolute -top-[3rem] left-1/2 transform -translate-x-1/2 z-10 min-w-[56px] min-h-[56px]"
            />
          )}

          {isRollbackTarget && (
            <div className="flex relative cursor-pointer items-center gap-3 px-3 py-2 rounded-full bg-[#FFFFFF0A]">
              <span className="font-semibold text-white">Previous</span>
            </div>
          )}
        </div>
      </motion.div>
    </>
  );
};

export default DeploymentBar;
