import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/hooks/use-toast";
import DeploymentBar from "./DeploymentBar";
import CustomDomainSection from "./CustomDomainSection";
import { useEffect } from "react";
import { useDeploy } from "@/hooks/useDeploy";

interface DeploymentRun {
  id: string;
  status: string;
  updated_at: string;
}

interface DeploymentHistoryViewProps {
  deploymentHistory: DeploymentRun[];
  jobId: string;
  handleDeploy: (image?: string, deployment_id?: string) => Promise<void>;
  deployStatus: string;
  runId?: string;
  deployUrl?: string;
  customDomainUrl?: string;
  latestRunStatus?: string | null;
  agentStatus?: string;
  onRollbackClick: (id: string) => void;
  onUnlinkDomain: (domain: string) => void;
  formatTimeAgo: (timestamp: string) => string;
  handleLogsClick?: () => void;
  deployLogs?: string[];
  loadDeployLogs?: (jobId: string) => Promise<string[]>;
}

export function DeploymentHistoryView({
  deploymentHistory,
  jobId,
  handleDeploy,
  deployStatus,
  runId,
  deployUrl,
  customDomainUrl,
  latestRunStatus,
  agentStatus,
  onRollbackClick,
  onUnlinkDomain,
  formatTimeAgo,
  handleLogsClick,
  deployLogs,
  loadDeployLogs,
}: DeploymentHistoryViewProps) {

  const { toast } = useToast();

   const {
      customDomain,
      loading,
      registerDomain,
      verifyDomain,
      connectDomain,
      checkDeployStatus
    } = useDeploy(jobId);

  const handleLoadLogs = async (jobId: string) => {
    if (loadDeployLogs) {
      try {
        const logs = await loadDeployLogs(jobId);
        console.log("xoxo Loaded logs:", logs, deployLogs);
      } catch (error) {
        console.error("Error loading logs:", error);
      }
    }
  };

  useEffect(() => {
    if (jobId) {
      handleLoadLogs(jobId);
    }
  }, [jobId]);

  const getDeployedUrl = () : string =>{
    if (customDomain && customDomain.status === "verified") {
      return customDomain?.domain.includes("http") ? customDomain.domain : `https://${customDomain.domain}`;
    }
    return deployUrl || "";
  }
  

  return (
    <motion.div
      className="relative p-3 pb-5 md:p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
      }}
      key="deployment-history"
    >
      <div className="max-h-[400px] relative z-[5] flex flex-col gap-8 overflow-y-auto overflow-clip border border-[#FFFFFF1F] rounded-[12px] p-2 md:p-6">
        <AnimatePresence mode="wait">
          {deploymentHistory && deploymentHistory.length > 0 ? (
            <motion.div
              key="history-list"
              className="flex relative flex-col gap-4 md:gap-8 z-[10]"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {deploymentHistory.map((deployment, index) => (
                <motion.div
                  key={`history-${deployment.id}`}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 30,
                    delay: index * 0.1, // Reduced delay for faster appearance
                  }}
                  className="z-[10]"
                >
                  <DeploymentBar
                    jobId={jobId}
                    showLogs={index === 0}
                    handleDeploy={handleDeploy}
                    deployStatus={deployment.status}
                    currentActive={
                      deployment.id === runId &&
                      deployment.status === "success"
                    }
                    deployTime={formatTimeAgo(deployment.updated_at)}
                    nextExist={index < deploymentHistory.length - 1}
                    deploymentLink={getDeployedUrl()}
                    deploymentRun={deployment as any}
                    latestRunStatus={latestRunStatus as any}
                    handleLogsClick={handleLogsClick}
                    onRollbackClick={(id) => {
                      //console.log("Rollback clicked for id:", id);
                      if (agentStatus === "running") {
                        toast({
                          title: "Agent is Running",
                          description:
                            "Please wait for agent to finish or stop it manually before rolling back",
                          variant: "destructive",
                          duration: 2000,
                        });
                        return;
                      }
                      onRollbackClick(id);
                    }}
                  />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              key="no-history"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="py-4 text-center text-gray-400">
                No deployment history available
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Custom Domain Section - only show for successful deployments */}
      {deployStatus === "success" && (
        <CustomDomainSection
          jobId={jobId}
          deployStatus={deployStatus}
          customDomain={customDomain}
          loading={loading}
          registerDomain={registerDomain}
          verifyDomain={verifyDomain}
          connectDomain={connectDomain}
          checkDeployStatus={checkDeployStatus}
          onUnlinkDomain={onUnlinkDomain}
        />
      )}
    </motion.div>
  );
}
