import React, { useState, useRef, useEffect } from "react";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
// import { useToast } from "@/hooks/use-toast";
import { Check, X } from "lucide-react";
import InfoSVG from "@/assets/panels/info.svg"
import ShareLinkSVG from "@/assets/panels/shareLink.svg"
import LinkSVG from "@/assets/panels/link.svg"
import CloseSVG from "@/assets/panels/close.svg"
import GreenSVG from "@/assets/panels/green_tick.svg"
import { cn } from "@/lib/utils";
import { URL_LINKS } from "@/constants/constants";

interface ShareModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  shareableLink: string;
  fromShowcase?: boolean;
}

export const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onOpenChange,
  shareableLink,
  fromShowcase = false,
}) => {
  // const { toast } = useToast();
  const [isCopied, setIsCopied] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareableLink);

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set copied state to true
      setIsCopied(true);

      // Reset after 2 seconds
      timeoutRef.current = setTimeout(() => {
        setIsCopied(false);
        timeoutRef.current = null;
      }, 1000);
    } catch (error) {
      console.error("Failed to copy link:", error);
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-1 md:m-4 mx-auto max-w-[calc(100vw-12px)] md:w-[650px] bg-[#18181A]  font-['Inter']">
        <div className="flex flex-col gap-3 p-3 sm:p-4 md:gap-8 md:p-8">
          <div className="flex flex-col md:gap-[10px]">
            <div className="flex items-start justify-between">
              <h2 className="text-[18px] md:text-[22px] text-[#FFFFFF] font-medium leading-[32px]">
                Share your app
              </h2>
              <DialogClose asChild>
                <button
                  type="button"
                  title="Close"
                  className="w-6 h-6 flex items-center justify-center text-[#737780] hover:text-white transition-colors"
                >
                  <img src={CloseSVG} alt="Close" className="w-5 h-5" />
                </button>
              </DialogClose>
            </div>

            <p className="text-[12px] md:text-[15px] font-medium md:leading-[24px] text-[#737780] font-['Inter'] max-md:flex gap-2 max-md:flex-col">
              {fromShowcase ? 
              "Share this incredible app and spread the word about what our community is building."
               : 
               "Share a live preview of your app with anyone. Perfect for gathering feedback or quick testing before deployment."}{" "}
              {!fromShowcase && <a
                href={URL_LINKS.differenceBetweenDeployments}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#2EBBE5] hover:text-[#2EBBE5]/80 underline transition-colors font-['Inter']"
              >
                Learn about sharing
              </a>}
            </p>
          </div>

          <div className="pt-2 space-y-2 md:space-y-4">
            <div className="relative hover:bg-[#ffffff16] group truncate cursor-pointer md:min-h-[56px] bg-[#ffffff10] w-full  rounded-[8px] overflow-hidden flex items-center" onClick={() => {
                window.open(shareableLink, "_blank");
              }}>
              <div className="flex items-center justify-between w-full h-full truncate cursor-pointer">
                <div className="flex-1 px-4 py-3 max-w-[90%] truncate">
                  <span className="text-[#fff]/80 group-hover:text-[#fff] text-[12px]  md:text-[16px] font-['Inter'] break-all text-nowrap font-medium">
                    {shareableLink.slice(0, 60)}
                  </span>
                </div>
                <div className="p-2 md:p-4">
                  <img src={LinkSVG} alt="Link" className="w-5 h-5" />
                </div>
              </div>
            </div>

            <button
              type="button"
              onClick={handleCopyLink}
              className={cn("w-full bg-white hover:bg-white/90 text-[#0F0F10] md:h-[48px] font-semibold py-3 px-4 rounded-[8px] flex items-center justify-center gap-2 transition-colors", isCopied && "bg-[#1A1A1C] border border-[#FFFFFF1F] text-[#FFFFFF] hover:bg-[#1A1A1C]")}>
              <span className="text-[14px] md:text-[18px] font-semibold">
                {isCopied ? "Copied" : "Copy Link"}
              </span>
              {isCopied ? (
                <Check className="w-6 h-6" />
              ) : (
                <img src={ShareLinkSVG} alt="Share Link" className="w-6 h-6" />
              )}
            </button>
          </div>

          {!fromShowcase && <div className="bg-[#FFAE6614] rounded-[8px] p-4 flex items-start gap-3">
            <img src={InfoSVG} alt="Info" className="w-6 h-6 flex-shrink-0 mt-0.5" />
            <div className="text-[12px] md:text-[15px] text-[#FFAE66] leading-[20px] font-['Inter'] font-medium">
              <span className="font-['Inter'] font-medium">
                Preview links expire in 30 minutes.
              </span>{" "}
              Deploy your app to get a permanent, shareable link.
            </div>
          </div>}
        </div>
      </DialogContent>
    </Dialog>
  );
};
