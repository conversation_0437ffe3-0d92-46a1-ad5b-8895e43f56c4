import { RefreshCcw, Loader2 } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import AgentSleepingSVG from "@/assets/agentsleeping.svg";
import AgentBell from "@/assets/fluent-emoji_bell.svg";
import AlertFillSVG from "@/assets/alert-fill.svg";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import CloseSVG from "@/assets/panels/close.svg"
import LinkSVG from "@/assets/panels/link.svg"
import ShareSVG from "@/assets/panels/share.svg"
import RefreshSvg from "@/assets/panels/refresh.svg"
import { ShareModal } from "@/components/sidebar/ShareModal"

interface PortMapping {
  service: string;
  hostPort: number;
  containerPort: number;
}

interface UrlPreviewPanelProps {
  isOpen: boolean;
  onClose: () => void;
  previewUrl: string;
  shareableLink: string;
  portMappings?: PortMapping[];
  podIsPaused?: boolean;
  showCase?: boolean;
  onResumePod?: () => void;
  isFromGithub?: any;
  shouldReloadIframe?: boolean;
  onIframeReloaded?: () => void;
  isResizing?: boolean;
}

export function UrlPreviewPanel({
  isOpen,
  onClose,
  previewUrl,
  shareableLink,
  showCase,
  podIsPaused = false,
  onResumePod,
  isFromGithub,
  shouldReloadIframe = false,
  onIframeReloaded,
  isResizing = false,
}: UrlPreviewPanelProps) {
  const [iframeLoading, setIframeLoading] = useState(true);
  const [loadingFailed, setLoadingFailed] = useState(false);
  const [loadingErrorMessage, setLoadingErrorMessage] = useState(
    "The preview could not be loaded. Please try again.",
  );
  const [autoRetrying, setAutoRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [showShareModal, setShowShareModal] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [key, setKey] = useState(0);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const autoRetryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const MAX_AUTO_RETRIES = 3;
  const RETRY_DELAY_MS = 3000; // 3 seconds between retries

  // Handle iframe reload when URL or panel state changes
  useEffect(() => {
    if (isOpen && previewUrl && !podIsPaused) {
      setRetryCount(0); // Reset retry count when URL changes
      handleRefresh();
    }

    return () => {
      // Clean up any pending timers when component is unmounted or URL changes
      clearAllTimers();
    };
  }, [isOpen, previewUrl, podIsPaused]);

  // Handle iframe reload when requested (after wakeup)
  useEffect(() => {
    if (shouldReloadIframe && previewUrl && !podIsPaused) {
      //console.log("Reloading iframe after wakeup");
      setRetryCount(0); // Reset retry count on explicit reload
      handleRefresh();

      // Notify parent that reload has been initiated
      if (onIframeReloaded) {
        onIframeReloaded();
      }
    }
  }, [shouldReloadIframe, previewUrl, podIsPaused]);

  // Clear all timers to prevent memory leaks
  const clearAllTimers = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }

    if (autoRetryTimeoutRef.current) {
      clearTimeout(autoRetryTimeoutRef.current);
      autoRetryTimeoutRef.current = null;
    }
  };

  const handleOpenInNewTab = () => {
    if (shareableLink || previewUrl) {
      window.open(showCase ? previewUrl : shareableLink, "_blank");
    }
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  // Auto-retry mechanism for 502 errors
  const scheduleAutoRetry = (error: string = "502") => {
    if (retryCount >= MAX_AUTO_RETRIES) {

      setAutoRetrying(false);
      setLoadingFailed(true);
      setLoadingErrorMessage(
        `Service returned ${error}. Tried ${MAX_AUTO_RETRIES} times but couldn't connect. The service might be starting up.`,
      );
      return;
    }

    const nextRetryCount = retryCount + 1;

    setAutoRetrying(true);
    setLoadingErrorMessage(
      `Service returned ${error}. Auto-retrying (${nextRetryCount}/${MAX_AUTO_RETRIES})...`,
    );

    // Clear any existing retry timeout
    if (autoRetryTimeoutRef.current) {
      clearTimeout(autoRetryTimeoutRef.current);
    }

    // Schedule the retry
    autoRetryTimeoutRef.current = setTimeout(() => {
      setRetryCount(nextRetryCount);
      //console.log(`Executing auto-retry ${nextRetryCount}/${MAX_AUTO_RETRIES}`);
      handleRefresh();
    }, RETRY_DELAY_MS);
  };

  // Improved loading mechanism with progressive fallbacks and auto-retry
  const handleRefresh = () => {
    if (!previewUrl) return;

    //console.log("Refreshing iframe with URL:", previewUrl);

    // Reset states for new loading attempt
    setIframeLoading(true);
    setLoadingFailed(false);

    if (!autoRetrying) {
      // Only reset error message if not in auto-retry mode
      setLoadingErrorMessage(
        "The preview could not be loaded. Please try again.",
      );
    }

    clearAllTimers();

    // Force iframe recreation by changing the key
    setKey((prevKey) => prevKey + 1);

    // Set up a loading timeout
    loadingTimeoutRef.current = setTimeout(() => {
      if (iframeLoading) {
        //console.log("Loading timeout reached after 15 seconds");

        // Don't show error UI if we're auto-retrying
        if (autoRetrying) {
          // Continue with next retry
          scheduleAutoRetry("timeout");
        } else {
          setLoadingFailed(true);
          setIframeLoading(false);
          setLoadingErrorMessage(
            "Loading timed out. The service might be starting up or experiencing issues.",
          );
        }
      }
    }, 15000);

    // Create a ping mechanism that checks if the iframe is responsive
    pingIntervalRef.current = setInterval(() => {
      // If already loaded or failed (and not auto-retrying), stop pinging
      if (!iframeLoading || (loadingFailed && !autoRetrying)) {
        if (pingIntervalRef.current) {
          clearInterval(pingIntervalRef.current);
          pingIntervalRef.current = null;
        }
        return;
      }

      // Try to detect if iframe has content
      const iframe = iframeRef.current;
      if (iframe) {
        try {
          // Check if iframe has rendered content
          const hasContent =
            iframe.contentWindow &&
            iframe.contentWindow.document &&
            iframe.contentWindow.document.body &&
            iframe.contentWindow.document.body.scrollHeight > 0;

          if (hasContent) {
            // Success! Content is detected
            //console.log("Iframe content detected via ping mechanism");
            setIframeLoading(false);
            setAutoRetrying(false);
            clearAllTimers();
          }
        } catch (e) {
          // CORS restrictions prevent accessing contentWindow
          //console.log("Cannot inspect iframe due to CORS restrictions");
        }
      }
    }, 1000);
  };

  return (
    <div
      className={cn(`w-full h-[calc(100%-3.5rem)] bg-[#111112]  max-md:absolute max-md:inset-0`, isOpen ? "block":"hidden")}
    >
      <div className="flex flex-col h-full">
        <div className="p-4 md:px-4 bg-[#111112] md:bg-transparent md:py-4 flex items-center justify-between border-b border border-[#242424]/60">
          <div className="text-[#939399] font-['Brockmann']  text-[15px]  md:text-[18px] font-medium leading-[24px]">
            Preview
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              {previewUrl && (
                    <button
                      type="button"
                      onClick={handleShareClick}
                      className="flex items-center justify-center h-8 bg-white hover:bg-white/90 rounded-[6px] gap-1 p-1 pr-2"
                    >
                      <img src={ShareSVG} alt="Share app" className="w-5 h-5" />
                      <span className="text-[#0F0F10] font-semibold text-[14px]">Share</span>
                    </button>
              )}
              {previewUrl && (
                <Tooltip delayDuration={100}>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      onClick={handleOpenInNewTab}
                      className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                    >
                      <img src={LinkSVG} alt="Open in new tab" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-[#DDDDE6] text-black border-0"
                    style={{ fontFamily: "Inter", fontWeight: 500 }}
                  >
                    Open in new tab
                  </TooltipContent>
                </Tooltip>
              )}
              {previewUrl && (
                <Tooltip delayDuration={100}>
                  <TooltipTrigger asChild>
                    <button
                      type="button"
                      onClick={handleRefresh}
                      className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                    >
                      <img src={RefreshSvg} alt="Refresh" className="w-5 h-5" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-[#DDDDE6] text-black border-0"
                    style={{ fontFamily: "Inter", fontWeight: 500 }}
                  >
                    Refresh
                  </TooltipContent>
                </Tooltip>
              )}
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    onClick={onClose}
                    className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                  >
                    <img src={CloseSVG} alt="Close" className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent
                  side="bottom"
                  className="bg-[#DDDDE6] text-black border-0"
                  style={{ fontFamily: "Inter", fontWeight: 500 }}
                >
                  Close
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="flex-1 p-0 overflow-hidden">
          {podIsPaused && !showCase ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <div className="w-full z-[20] px-[2rem] gap-6 rounded-[1rem] rounded-bl-none rounded-br-none pt-[2rem] pb-[3rem] flex flex-col justify-between items-center">
                <div className="flex flex-col items-center gap-5">
                  <img
                    src={AgentSleepingSVG}
                    className="min-w-[100px] min-h-[100px]"
                    alt="Agent Sleeping"
                  />
                  <div className="flex flex-col items-center gap-3">
                    <span className="text-[#C4C4CC] text-[20px]">
                      Preview paused - Agent is sleeping
                    </span>
                    {/* Show different messages based on GitHub status and creation date */}
                    {isFromGithub ? (
                      <span className="text-[#7b7b80] text-sm">
                        Apologies, the task has expired due to inactivity. We
                        may not be able to recover this at this moment. Please
                        start a new task using the same github repo. Reach out
                        to us at{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="text-[#ACACB2] font-extrabold underline underline-offset-1"
                        >
                          <EMAIL>
                        </a>{" "}
                        for any assistance.
                      </span>
                    ) : (
                      <span className="text-[#7b7b80] text-sm">
                        If you are having trouble accessing your work, Please
                        contact support at{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-semibold underline underline-offset-1"
                        >
                          <EMAIL>
                        </a>
                      </span>
                    )}
                  </div>
                </div>
                {isFromGithub ? null : (
                  <button
                    type="button"
                    onClick={onResumePod}
                    className="bg-[#FCB94920] p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
                  >
                    <img src={AgentBell} alt="Wake icon" className="w-5 h-5" />
                    <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-medium">
                      Wake up the Agent
                    </span>
                  </button>
                )}
              </div>
            </div>
          ) : previewUrl && isOpen ? (
            <div className="relative w-full h-full">
              {iframeLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
                  <div className="flex flex-col items-center">
                    <Loader2 className="w-8 h-8 text-[#5FD3F3] animate-spin mb-4" />
                    <p className="text-[#939399]">Loading preview...</p>
                  </div>
                </div>
              )}

              {loadingFailed && (
                <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
                  <div className="flex flex-col items-center max-w-md gap-6 text-center">
                    <img src={AlertFillSVG} alt="Alert" className="w-12 h-12" />
                    <div className="flex flex-col gap-2">
                      <p className="text-[#C4C4CC] text-[24px] font-medium">
                        {autoRetrying
                          ? "Retrying..."
                          : "Preview failed to load"}
                      </p>
                      <p className="text-[#939399]">{loadingErrorMessage}</p>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      {!autoRetrying && (
                        <button
                          type="button"
                          onClick={handleRefresh}
                          className="bg-[#FCB94920] p-[10px] rounded-[6px] flex items-center gap-2 hover:bg-[#FCB94930] transition-colors duration-200"
                        >
                          <RefreshCcw className="w-5 h-5 text-[#FCB949]" />
                          <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-medium">
                            Reload Preview
                          </span>
                        </button>
                      )}
                      {autoRetrying && (
                        <div className="flex items-center gap-2 text-[#939399]">
                          <Loader2 className="w-5 h-5 text-[#FCB949] animate-spin" />
                          <span className="text-sm">
                            Auto-retrying ({retryCount}/{MAX_AUTO_RETRIES})
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {(iframeLoading || (!loadingFailed && !autoRetrying)) && (
                <iframe
                  ref={iframeRef}
                  key={key}
                  src={previewUrl}
                  className="w-full h-full border-0"
                  style={{ pointerEvents: isResizing ? 'none' : 'auto' }}
                  onLoad={() => {
                    //console.log("Iframe onLoad event fired");

                    // Check if iframe actually loaded meaningful content
                    try {
                      const iframe = iframeRef.current;

                      // Try to access iframe content - this might throw CORS errors
                      if (
                        iframe &&
                        iframe.contentWindow &&
                        iframe.contentWindow.document
                      ) {
                        const iframeDoc = iframe.contentWindow.document;
                        const title = iframeDoc.title || "";
                        const body = iframeDoc.body?.textContent || "";

                        // Check for common error patterns with special handling for 502
                        const is502Error =
                          title.includes("502") ||
                          body.includes("502") ||
                          title.includes("Bad Gateway") ||
                          body.includes("Bad Gateway");

                        const otherErrorPatterns = [
                          "404",
                          "Not Found",
                          "Error",
                          "unavailable",
                          "Service Unavailable",
                        ];
                        const hasOtherError = otherErrorPatterns.some(
                          (pattern) =>
                            title.includes(pattern) || body.includes(pattern),
                        );

                        if (is502Error) {
                          console.error(
                            "Iframe loaded with 502 Bad Gateway error",
                          );

                          // Initiate auto-retry for 502 errors
                          scheduleAutoRetry("502 Bad Gateway");

                          setLoadingFailed(true);
                          setIframeLoading(false);
                          return;
                        } else if (hasOtherError) {
                          console.error(
                            "Iframe loaded with error content:",
                            title,
                          );
                          setLoadingFailed(true);
                          setLoadingErrorMessage(
                            "The service returned an error page. It might be experiencing issues.",
                          );
                          setIframeLoading(false);
                          return;
                        }
                      }
                    } catch (e) {
                    }

                    setIframeLoading(false);
                    setAutoRetrying(false);
                    setLoadingFailed(false);

                    clearAllTimers();
                  }}
                  onError={(e) => {
                    console.error("Iframe failed to load with error:", e);

                    // Check for specific error patterns
                    const errorString = String(e);
                    const is502Error =
                      errorString.includes("502") ||
                      errorString.includes("Bad Gateway");

                    if (is502Error) {
                      // Initiate auto-retry for 502 errors
                      scheduleAutoRetry("502 Bad Gateway");
                    } else {
                      setLoadingFailed(true);
                      setLoadingErrorMessage(
                        "Failed to load the preview. Please check if the service is running.",
                      );
                      setAutoRetrying(false);
                    }

                    setIframeLoading(false);
                    clearAllTimers();
                  }}
                  title="URL Preview"
                  sandbox="allow-scripts allow-same-origin allow-forms"
                />
              )}
            </div>
          ) : null}
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onOpenChange={setShowShareModal}
        shareableLink={showCase ? previewUrl : shareableLink}
        fromShowcase={showCase}
      />
    </div>
  );
}



