"use client"

import * as React from "react"
import * as ResizablePrimitive from "react-resizable-panels"
import { motion, AnimatePresence } from "framer-motion"

import { cn } from "@/lib/utils"

function ResizablePanelGroup({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) {
  return (
    <ResizablePrimitive.PanelGroup
      data-slot="resizable-panel-group"
      className={cn(
        "flex h-full w-full data-[panel-group-direction=vertical]:flex-col",
        className
      )}
      {...props}
    />
  )
}

function ResizablePanel({
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.Panel>) {
  return <ResizablePrimitive.Panel data-slot="resizable-panel" {...props} />
}

// Animated version of ResizablePanel with smooth entry/exit animations
function AnimatedResizablePanel({
  isVisible = true,
  animationConfig = {
    type: "spring",
    stiffness: 300,
    damping: 30,
    mass: 0.8,
    bounce: 0.1
  },
  exitDelay = 400,
  onAnimationComplete,
  onResize,
  isDragging = false,
  children,
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.Panel> & {
  isVisible?: boolean;
  animationConfig?: any;
  exitDelay?: number;
  onAnimationComplete?: () => void;
  onResize?: (size: number) => void;
  isDragging?: boolean;
}) {
  const [shouldRender, setShouldRender] = React.useState(isVisible);
  const [isExiting, setIsExiting] = React.useState(false);
  const [panelSize, setPanelSize] = React.useState(30); // Track current panel size
  const [wasDragging, setWasDragging] = React.useState(false); // Track previous drag state

  React.useEffect(() => {
    if (isVisible) {
      setShouldRender(true);
      setIsExiting(false);
    } else if (shouldRender && !isExiting) {
      setIsExiting(true);
      // Don't remove from DOM immediately - wait for animation
      const timer = setTimeout(() => {
        setShouldRender(false);
        setIsExiting(false);
        onAnimationComplete?.();
      }, exitDelay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, shouldRender, exitDelay, onAnimationComplete, isExiting]);

  // Track drag state changes to prevent sudden shifts
  React.useEffect(() => {
    setWasDragging(isDragging);
  }, [isDragging]);

  // Always render the panel during exit animation to prevent sudden layout changes
  if (!shouldRender && !isExiting) return null;

  return (
    <motion.div
      className="h-full overflow-hidden"
      initial={{ width: 0 }}
      animate={{
        width: (isVisible && !isExiting) ? `${panelSize}%` : 0
      }}
      exit={{ width: 0 }}
      transition={isDragging ? {
        duration: 0,
        ease: "linear"
      } : animationConfig}
      onAnimationComplete={() => {
        if (!isVisible && isExiting && !isDragging) {
          // Animation completed, now safe to remove from DOM
          setShouldRender(false);
          setIsExiting(false);
          onAnimationComplete?.();
        }
      }}
    >
      <ResizablePrimitive.Panel
        data-slot="resizable-panel"
        className={cn("h-full w-full", className)}
        onResize={(size) => {
          setPanelSize(size);
          onResize?.(size);
        }}
        {...props}
      >
        <motion.div
          key="animated-panel-content"
          initial={{ x: '100%', opacity: 0 }}
          animate={isDragging ? { x: 0, opacity: 1 } : {
            x: isVisible ? 0 : '100%',
            opacity: isVisible ? 1 : 0
          }}
          transition={isDragging ? { duration: 0 } : {
            ...animationConfig,
            delay: isVisible ? 0.1 : 0 // Slight delay on entry for smoother effect
          }}
          className="w-full h-full"
        >
          {children}
        </motion.div>
      </ResizablePrimitive.Panel>
    </motion.div>
  );
}

function ResizableHandle({
  withHandle,
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {
  withHandle?: boolean
}) {
  const [isDragging, setIsDragging] = React.useState(false);

  return (
    <ResizablePrimitive.PanelResizeHandle
      data-slot="resizable-handle"
      className={cn(
        "bg-border focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:translate-x-0 data-[panel-group-direction=vertical]:after:-translate-y-1/2 [&[data-panel-group-direction=vertical]>div]:rotate-90 group hover:bg-[#00CCAF]/60 data-[resize-handle-state=drag]:bg-[#00CCAF]/60 transition-colors duration-200",
        className
      )}
      {...props}
      onDragging={(e) => {
        setIsDragging(e);
      }}
    >
      {withHandle && (
        <div className={cn("group-hover:bg-[#00CCAF] z-10 min-w-2 min-h-6 border bg-[#242424] border-[#242424] rounded-md hover:bg-[#00CCAF] hover:border-[#00CCAF] data-[resize-handle-state=drag]:bg-[#00CCAF] data-[resize-handle-state=drag]:border-[#00CCAF] transition-all duration-200", isDragging ? "bg-[#00CCAF]" : "")}>
        </div>
      )}
    </ResizablePrimitive.PanelResizeHandle>
  )
}

// Animated version of ResizableHandle with smooth entry/exit animations
function AnimatedResizableHandle({
  isVisible = true,
  animationConfig = {
    type: "spring",
    stiffness: 300,
    damping: 30,
    mass: 0.8
  },
  withHandle,
  className,
  onDragging,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {
  isVisible?: boolean;
  animationConfig?: any;
  withHandle?: boolean;
  onDragging?: (isDragging: boolean) => void;
}) {
  const [isDragging, setIsDragging] = React.useState(false);

  const handleDragging = React.useCallback((dragging: boolean) => {
    setIsDragging(dragging);
    onDragging?.(dragging);
  }, [onDragging]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, width: 0 }}
          animate={{ opacity: 1, width: 'auto' }}
          exit={{ opacity: 0, width: 0 }}
          transition={animationConfig}
        >
          <ResizablePrimitive.PanelResizeHandle
            data-slot="resizable-handle"
            className={cn(
              "bg-border h-full focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:translate-x-0 data-[panel-group-direction=vertical]:after:-translate-y-1/2 [&[data-panel-group-direction=vertical]>div]:rotate-90 group hover:bg-[#00CCAF]/60 data-[resize-handle-state=drag]:bg-[#00CCAF]/60 transition-colors duration-200",
              className
            )}
            {...props}
            onDragging={handleDragging}
          >
            {withHandle && (
              <div className={cn("group-hover:bg-[#00CCAF] z-10 min-w-2 min-h-6 border bg-[#242424] border-[#242424] rounded-md hover:bg-[#00CCAF] hover:border-[#00CCAF] data-[resize-handle-state=drag]:bg-[#00CCAF] data-[resize-handle-state=drag]:border-[#00CCAF] transition-all duration-200", isDragging ? "bg-[#00CCAF]" : "")}>
              </div>
            )}
          </ResizablePrimitive.PanelResizeHandle>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
  AnimatedResizablePanel,
  AnimatedResizableHandle
}
