import { Check } from 'lucide-react';
// @ts-ignore
import animatedSpinner from '../../assets/animated-spinner.gif';

type TaskStatus = 'done' | 'running' | 'pending';

export interface Task {
  id: string;
  label: string;
  status: TaskStatus;
}

interface TaskListProps {
  tasks: Task[];
  className?: string;
}

export function TaskList({ tasks, className = '' }: TaskListProps) {
  const getTaskIcon = (status: TaskStatus) => {
    switch (status) {
      case 'done':
        return <Check className="w-5 h-5 text-task-done" />;
      case 'running':
        return <img src={animatedSpinner} className="w-5 h-5" alt="Loading..." />;
      case 'pending':
        return <div className="w-5 h-5 border-2 rounded-full opacity-50 border-task-pending" />;
    }
  };

  const getTaskTextColor = (status: TaskStatus) => {
    switch (status) {
      case 'done':
        return 'text-task-done';
      case 'running':
        return 'text-task-running';
      case 'pending':
        return 'text-task-pending opacity-50';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {tasks.map((task) => (
        <div key={task.id} className="flex items-center space-x-3">
          {getTaskIcon(task.status)}
          <span className={`font-berkeley text-[14px]  md:text-base ${getTaskTextColor(task.status)}`}>
            {task.label}
          </span>
        </div>
      ))}
    </div>
  );
}
