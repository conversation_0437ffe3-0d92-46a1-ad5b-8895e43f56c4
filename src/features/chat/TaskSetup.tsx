import { BoxLoader } from "@/components/ui/box-loader";
import { Task, TaskList } from "@/components/ui/task-list";
import { useCallback, useEffect, useRef, useState } from "react";

interface ProgressStep {
  id: string;
  label: string;
  progressPoints: number[];
  minDuration: number;
  maxDuration: number;
}

interface TaskSetupLoaderProps {
  complete?: boolean;
  onComplete?: () => void;
}

type TaskStatus = "pending" | "running" | "done";

const cloudTasks: Task[] = [
  {
    id: "cloud-setup",
    label: "Initializing cloud environment",
    status: "pending",
  },
  {
    id: "cloud-provision",
    label: "Provisioning resources",
    status: "pending",
  },
  {
    id: "cloud-configure",
    label: "Configuring environment",
    status: "pending",
  },
  {
    id: "cloud-start",
    label: "Starting the agent",
    status: "pending",
  }
];

export default function TaskSetupLoader({ complete = false, onComplete }: TaskSetupLoaderProps): JSX.Element {
  const [tasks, setTasks] = useState<Task[]>(cloudTasks);
  const [progress, setProgress] = useState<number>(0);
  const [minBoxes, setMinBoxes] = useState<number>(14);
  const [maxBoxes, setMaxBoxes] = useState<number>(14);
  const [boxWidth, setBoxWidth] = useState<number>(24);
  const [boxHeight, setBoxHeight] = useState<number>(40);
  const [gapWidth, setGapWidth] = useState<number>(8);
  
  const progressTimer = useRef<NodeJS.Timeout | null>(null);
  const currentStepIndex = useRef<number>(0);
  const currentProgressRef = useRef<number>(0.05);
  const stepStartTime = useRef<number>(0);
  const currentPointIndex = useRef<number>(0);
  const nextUpdateTime = useRef<number>(0);
  const randomStoppingPoint = useRef<number>(90);
  const holdingProgress = useRef<boolean>(false);
  const jobStarted = useRef<boolean>(false);

  // Handle responsive box properties
  useEffect(() => {
    const updateBoxProperties = (): void => {
      if (window.innerWidth < 768) {
        setMinBoxes(12);
        setMaxBoxes(12);
        setBoxWidth(18);
        setBoxHeight(32);
        setGapWidth(6);
      } else {
        setMinBoxes(14);
        setMaxBoxes(14);
        setBoxWidth(24);
        setBoxHeight(40);
        setGapWidth(8);
      }
    };

    updateBoxProperties();
    window.addEventListener('resize', updateBoxProperties);
    return () => window.removeEventListener('resize', updateBoxProperties);
  }, []);

  const updateTaskStatus = useCallback((taskId: string, status: TaskStatus): void => {
    setTasks(current => {
      const updatedTasks = current.map(task =>
        task.id === taskId ? { ...task, status } : task
      );
      return updatedTasks;
    });
  }, []);

  const progressSteps: ProgressStep[] = [
    {
      id: "cloud-setup",
      label: "Initializing cloud environment",
      progressPoints: [0.05, 0.08, 0.12, 0.17, 0.22, 0.25],
      minDuration: 2500,
      maxDuration: 4000
    },
    {
      id: "cloud-provision",
      label: "Provisioning resources",
      progressPoints: [0.25, 0.29, 0.34, 0.38, 0.42, 0.45],
      minDuration: 2500,
      maxDuration: 4000
    },
    {
      id: "cloud-configure",
      label: "Configuring environment",
      progressPoints: [0.45, 0.51, 0.58, 0.63, 0.67, 0.70, 0.73, 0.76, 0.79, 0.82, 0.85, 0.88, 0.90],
      minDuration: 2000,
      maxDuration: 4000
    },
    {
      id: "cloud-start",
      label: "Starting the agent",
      progressPoints: [0.90, 0.92, 0.94, 0.95],
      minDuration: 0,
      maxDuration: 0
    }
  ];

  const getRandomDuration = (min: number, max: number): number => 
    Math.floor(min + Math.random() * (max - min));

  const startProgressAnimation = useCallback((): void => {
    randomStoppingPoint.current = 65 + Math.floor(Math.random() * 26); // 65-90%
    stepStartTime.current = Date.now();
    nextUpdateTime.current = Date.now() + Math.floor(500 + Math.random() * 1000);
    
    console.log(`Starting progress animation, stopping at ${randomStoppingPoint.current}%`);

    const updateProgress = (): void => {
      if (jobStarted.current) return;

      const now = Date.now();
      const currentStep = progressSteps[currentStepIndex.current];
      const randomStopPercent = randomStoppingPoint.current / 100;

      if (currentProgressRef.current >= randomStopPercent) {
        holdingProgress.current = true;
        
        // Pulse occasionally to show activity
        const pulseDelay = 3000 + Math.random() * 2000;
        progressTimer.current = setTimeout(() => {
          const pulseProgress = Math.min(randomStopPercent + 0.02, currentProgressRef.current + 0.01);
          currentProgressRef.current = pulseProgress;
          setProgress(pulseProgress);

          setTimeout(() => {
            if (!jobStarted.current) {
              currentProgressRef.current = randomStopPercent;
              setProgress(randomStopPercent);
              progressTimer.current = setTimeout(updateProgress, pulseDelay);
            }
          }, 1000);
        }, pulseDelay);
        return;
      }

      const stepElapsed = now - stepStartTime.current;

      if (now >= nextUpdateTime.current) {
        const progressPoints = currentStep.progressPoints;

        if (currentPointIndex.current < progressPoints.length - 1) {
          currentPointIndex.current++;
          const newProgress = progressPoints[currentPointIndex.current];
          
          currentProgressRef.current = newProgress;
          setProgress(newProgress);

          const randomDelay = Math.floor(800 + Math.random() * 1200);
          nextUpdateTime.current = now + randomDelay;
        } else {
          const totalDuration = getRandomDuration(currentStep.minDuration, currentStep.maxDuration);

          if (stepElapsed >= totalDuration) {
            updateTaskStatus(currentStep.id, "done");
            currentStepIndex.current += 1;
            currentPointIndex.current = 0;
            stepStartTime.current = now;

            if (currentStepIndex.current < progressSteps.length) {
              const nextStep = progressSteps[currentStepIndex.current];
              updateTaskStatus(nextStep.id, "running");

              const initialProgress = nextStep.progressPoints[0];
              currentProgressRef.current = initialProgress;
              setProgress(initialProgress);

              const randomDelay = Math.floor(800 + Math.random() * 1200);
              nextUpdateTime.current = now + randomDelay;
            }
          } else {
            nextUpdateTime.current = now + 500;
          }
        }
      }

      progressTimer.current = setTimeout(updateProgress, 200);
    };

    // Start first step
    const firstStep = progressSteps[0];
    updateTaskStatus(firstStep.id, "running");
    
    const initialProgress = firstStep.progressPoints[0];
    currentProgressRef.current = initialProgress;
    setProgress(initialProgress);

    updateProgress();
  }, [updateTaskStatus]);

  // Handle completion when complete prop changes to true
  useEffect(() => {
    if (complete && !jobStarted.current) {
      console.log('Completion triggered by prop...');
      jobStarted.current = true;
      holdingProgress.current = false;

      // Complete all remaining steps in sequence
      const completeRemainingSteps = (stepIndex: number): void => {
        if (stepIndex >= progressSteps.length) {
          // All steps completed, start final animation
          console.log('All steps completed, starting final animation');
          setTimeout(() => {
            animateToCompletion();
          }, 800);
          return;
        }

        const step = progressSteps[stepIndex];
        console.log(`Completing step ${stepIndex}: ${step.id} (current step index: ${currentStepIndex.current})`);

        // Calculate target progress for this step completion
        const stepProgress = (stepIndex + 1) / progressSteps.length;
        const targetProgress = Math.min(stepProgress * 0.95, 0.95); // Cap at 95% until final animation

        // If this is the current step or a previous step, mark it as done
        if (stepIndex <= currentStepIndex.current) {
          console.log(`Marking step ${stepIndex} (${step.id}) as done immediately`);
          updateTaskStatus(step.id, "done");

          // Update progress immediately
          currentProgressRef.current = targetProgress;
          setProgress(targetProgress);

          // Move to next step immediately
          setTimeout(() => {
            completeRemainingSteps(stepIndex + 1);
          }, 300);
        } else {
          // For future steps, mark as running first, then done
          console.log(`Marking step ${stepIndex} (${step.id}) as running, then done`);
          updateTaskStatus(step.id, "running");

          // Animate progress to intermediate point
          const intermediateProgress = targetProgress - 0.05;
          currentProgressRef.current = intermediateProgress;
          setProgress(intermediateProgress);

          setTimeout(() => {
            updateTaskStatus(step.id, "done");

            // Update progress to target when step is done
            currentProgressRef.current = targetProgress;
            setProgress(targetProgress);

            setTimeout(() => {
              completeRemainingSteps(stepIndex + 1);
            }, 300);
          }, 600);
        }
      };

      // Animate to completion function
      const animateToCompletion = (): void => {
        console.log(`Starting final animation from ${(currentProgressRef.current * 100).toFixed(1)}% to 100%`);

        const startValue = currentProgressRef.current;
        const endValue = 1.0;
        const duration = 2500;
        const startTime = Date.now();

        const updateFinalProgress = (): void => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);
          const easeProgress = 1 - (1 - progress) * (1 - progress);
          const newProgress = startValue + (endValue - startValue) * easeProgress;

          currentProgressRef.current = newProgress;
          setProgress(newProgress);

          if (progress < 1) {
            requestAnimationFrame(updateFinalProgress);
          } else {
            console.log('Final animation complete at 100%');
            // Animation is complete, call onComplete callback if provided
            if (onComplete) {
              setTimeout(() => {
                onComplete();
              }, 500); // Small delay to ensure visual completion
            }
          }
        };

        requestAnimationFrame(updateFinalProgress);
      };

      // Start completing steps from current step
      setTimeout(() => {
        completeRemainingSteps(currentStepIndex.current);
      }, 500);
    }
  }, [complete, updateTaskStatus, onComplete]);

  // Start the animation on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      startProgressAnimation();
    }, 1000);

    return () => {
      clearTimeout(timer);
      if (progressTimer.current) {
        clearTimeout(progressTimer.current);
      }
    };
  }, [startProgressAnimation]);

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-lg min-h-screen p-4 mx-auto my-auto space-y-4 md:space-y-10">
      <div className="flex flex-col space-y-4 md:space-y-10">
        <div className="space-y-1 text-left md:w-full md:space-y-3">
          <h1 className="font-sans text-[22px] md:text-[32px] font-medium leading-[40px] tracking-[-0.64px] text-loader">
            Setting Up Safe Environment
          </h1>
          <p className="font-berkeley text-[14px] md:text-base text-[#DDDDE6]/50 text-left">
            // This usually takes around 1-3 mins
          </p>
        </div>

        <BoxLoader
          boxWidth={boxWidth}
          boxHeight={boxHeight}
          gapWidth={gapWidth}
          progress={progress}
          className="md:w-full"
          minBoxes={minBoxes}
          maxBoxes={maxBoxes}
        />
        
        <TaskList tasks={tasks} className="md:w-full" />
      </div>
    </div>
  );
}