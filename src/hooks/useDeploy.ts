// features/deploy/useDeploy.ts
import { useEffect, useState, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { store } from '@/store';
import { agentApi } from '@/services/agentApi';
import {
  checkDeployStatus,
  deployApp,
  loadDeploymentHistory,
  shutDownDeployment,
  loadEnvironmentVariables,
  saveEnvironmentVariables,
  selectDeployStatus,
  selectLatestRunStatus,
  selectDeploymentSteps,
  selectDeploymentHistory,
  selectCurrentStepIndex,
  selectDeployUrl,
  selectCustomDomainUrl,
  selectRunId,
  selectErrorMessage,
  selectCurrentJobId,
  selectModifiedEnvs,
  selectCustomDomain,
  setActiveTabId as setActiveTabIdAction,
  resetDeployState,
  resetDeploymentSteps,
  setCurrentJobId as setCurrentJobIdAction,
  setCustomDomain as setCustomDomainAction,
  EnvValues,
  selectDeployLogs,
  getDeployLogs
} from '@/store/deploySlice';

// Simplified PollingManager - Only tracks active tab polling
class PollingManager {
  private static instance: PollingManager;
  private pollingInterval: NodeJS.Timeout | null = null;
  private activeJobId: string | null = null;
  private activeTabId: string | null = null;

  private constructor() {}

  public static getInstance(): PollingManager {
    if (!PollingManager.instance) {
      PollingManager.instance = new PollingManager();
    }
    return PollingManager.instance;
  }

  public startPolling(jobId: string, pollingFn: () => void, tabId: string, interval: number = 15000): void {
    //console.log(`[PollingManager] Starting polling for jobId ${jobId} in tab ${tabId}`);

    // If we're already polling for this job, don't start a new one
    if (this.activeJobId === jobId && this.pollingInterval !== null) {
      //console.log(`[PollingManager] Already polling for jobId ${jobId}`);
      return;
    }

    // Stop any existing polling
    this.stopPolling();

    // Set as the active job ID and tab ID
    this.activeJobId = jobId;
    this.activeTabId = tabId;

    // Execute the polling function immediately
    pollingFn();

    // Start a new interval
    this.pollingInterval = setInterval(pollingFn, interval);

    //console.log(`[PollingManager] Polling started for jobId ${jobId} in tab ${tabId}`);
  }

  public stopPolling(): void {
    if (this.pollingInterval === null) {
      //console.log(`[PollingManager] No active polling to stop`);
      return;
    }

    //console.log(`[PollingManager] Stopping polling for jobId ${this.activeJobId}`);

    // Clear the interval
    clearInterval(this.pollingInterval);
    this.pollingInterval = null;

    // Clear the active job ID and tab ID
    const oldJobId = this.activeJobId;
    const oldTabId = this.activeTabId;

    this.activeJobId = null;
    this.activeTabId = null;

    //console.log(`[PollingManager] Polling stopped for jobId ${oldJobId} in tab ${oldTabId}`);
  }

  public isPollingActive(jobId: string): boolean {
    return this.pollingInterval !== null && this.activeJobId === jobId;
  }

  public getActiveJobId(): string | null {
    return this.activeJobId;
  }

  public setActiveTabId(tabId: string): void {
    //console.log(`[PollingManager] Setting active tab ID to ${tabId}`);

    // If the active tab hasn't changed, do nothing
    if (this.activeTabId === tabId) {
      return;
    }

    // If we were polling for a different tab, stop polling
    if (this.activeTabId !== null && this.activeTabId !== tabId && this.pollingInterval !== null) {
      //console.log(`[PollingManager] Stopping polling for jobId ${this.activeJobId} because tab changed`);
      this.stopPolling();
    }

    this.activeTabId = tabId;
  }

  public getActiveTabId(): string | null {
    return this.activeTabId;
  }
}


const pollingManager = PollingManager.getInstance();

export const useDeploy = (jobId?: string) => {
  const dispatch = useAppDispatch();
  const previousJobIdRef = useRef<string | undefined>(jobId);

  // Global state from Redux
  const globalDeployStatus = useAppSelector(selectDeployStatus);
  const globalLatestRunStatus = useAppSelector(selectLatestRunStatus);
  const globalDeploymentSteps = useAppSelector(selectDeploymentSteps);
  const globalDeploymentHistory = useAppSelector(selectDeploymentHistory);
  const globalCurrentStepIndex = useAppSelector(selectCurrentStepIndex);
  const globalDeployUrl = useAppSelector(selectDeployUrl);
  const globalCustomDomainUrl = useAppSelector(selectCustomDomainUrl);
  const globalRunId = useAppSelector(selectRunId);
  const globalErrorMessage = useAppSelector(selectErrorMessage);
  const globalCurrentJobId = useAppSelector(selectCurrentJobId);
  const globalModifiedEnvs = useAppSelector(selectModifiedEnvs);
  const globalCustomDomain = useAppSelector(selectCustomDomain);
  const globalDeployLogs = useAppSelector(selectDeployLogs);

  // Local state - only used when we need to filter based on jobId
  const [localLoading, setLocalLoading] = useState(false);

  // Reference to track the last API call timestamp
  const lastApiCallRef = useRef<number>(0);

  // Effect to check deploy status when jobId changes or is provided
  useEffect(() => {
    if (jobId) {
      // Only check if the job ID has changed
      if (jobId !== previousJobIdRef.current) {
        //console.log(`[useDeploy] Job ID changed to ${jobId}, checking status`);

        // Update the current job ID in Redux
        dispatch(setCurrentJobIdAction(jobId));

        // Check the deployment status for the new job ID
        dispatch(checkDeployStatus({ jobId, silent: true }));
        previousJobIdRef.current = jobId;
      }
    }
  }, [jobId, dispatch]);

  // Function to check if we should make an API call based on time interval
  const shouldMakeApiCall = useCallback(() => {
    const now = Date.now();
    const MIN_INTERVAL = 15000; // 15 seconds

    if (now - lastApiCallRef.current >= MIN_INTERVAL) {
      lastApiCallRef.current = now;
      return true;
    }

    return false;
  }, []);

  // Create a polling function that can be passed to the polling manager
  const createPollingFunction = useCallback((jobId: string) => {
    return () => {

      // Make sure we respect the time interval
      if (!shouldMakeApiCall()) {
        return;
      }

      // Make a direct API call to check deployment status
      try {
        agentApi.getDeployStatus(jobId)
          .then(response => {
            //console.log(`[useDeploy] Polling API response:`, response);

            // Get the current state to include the previous status
            const state = store.getState();
            const prevStatus = state?.deploy?.deployStatus;

            // Update Redux state with the response
            dispatch({
              type: 'deploy/checkDeployStatus/fulfilled',
              payload: { response, timestamp: Date.now(), prevStatus },
              meta: { arg: { jobId, silent: true }, requestId: 'polling', requestStatus: 'fulfilled' }
            });

            // Check if deployment is still running
            const isStillRunning =
              response.status === 'running' ||
              (response.latest_run && response.latest_run.status === 'running');

            if (!isStillRunning) {
              //console.log(`[useDeploy] Deployment is no longer running, stopping polling`);

              // If the status changed to success, force one more status check to ensure UI updates
              if (response.status === 'success' || (response.latest_run && response.latest_run.status === 'success')) {
                //console.log(`[useDeploy] Deployment succeeded, ensuring UI is updated`);

                // Force a non-silent status check to ensure UI updates
                dispatch(checkDeployStatus({ jobId, silent: false }));
              }

              pollingManager.stopPolling();
            }
          })
          .catch(error => {
            console.error(`[useDeploy] Polling API error:`, error);
          });
      } catch (error) {
        console.error(`[useDeploy] Error in polling function:`, error);
      }
    };
  }, [dispatch, shouldMakeApiCall]);

  // Function to stop polling
  const stopPolling = useCallback(() => {
    //console.log(`[useDeploy] stopPolling called`);
    pollingManager.stopPolling();
  }, []);

  // Function to start polling
  const startPolling = useCallback((jobIdToStart: string) => {
    //console.log(`[useDeploy] startPolling called for jobId ${jobIdToStart}`);

    // Check if this tab is active using the current Redux state
    const state = store.getState();
    const activeTabId = state?.tabs?.activeTab;

    if (!activeTabId) {
      //console.log(`[useDeploy] No active tab, not starting polling`);
      return;
    }

    // Find the active tab
    const activeTab = state?.tabs?.tabs ?
      state.tabs.tabs.find((tab: any) => tab.id === activeTabId) :
      undefined;

    // Only start polling if this jobId belongs to the active tab
    if (activeTab?.state?.jobId !== jobIdToStart) {
      //console.log(`[useDeploy] JobId ${jobIdToStart} doesn't belong to active tab, not starting polling`);
      return;
    }

    // First make a direct API call to check if the deployment is running
    try {
      //console.log(`[useDeploy] Making initial API call to check status for ${jobIdToStart}`);
      agentApi.getDeployStatus(jobIdToStart)
        .then(response => {
          //console.log(`[useDeploy] Initial API call response:`, response);

          // Get the current state to include the previous status
          const state = store.getState();
          const prevStatus = state?.deploy?.deployStatus;

          // Update Redux state with the response
          dispatch({
            type: 'deploy/checkDeployStatus/fulfilled',
            payload: { response, timestamp: Date.now(), prevStatus },
            meta: { arg: { jobId: jobIdToStart, silent: true }, requestId: 'initial-check', requestStatus: 'fulfilled' }
          });

          // Check if we need to start polling
          const isRunning =
            response.status === 'running' ||
            (response.latest_run && response.latest_run.status === 'running');

          if (isRunning) {
            //console.log(`[useDeploy] Deployment is running, starting polling`);

            // Create a polling function for this job
            const pollingFn = createPollingFunction(jobIdToStart);

            // Start polling using the centralized manager with the tab ID
            pollingManager.startPolling(jobIdToStart, pollingFn, activeTabId);
          } else if (response.status === 'success' || (response.latest_run && response.latest_run.status === 'success')) {
            // If the deployment is already successful, force a non-silent status check to ensure UI updates
            //console.log(`[useDeploy] Deployment is already successful, ensuring UI is updated`);
            dispatch(checkDeployStatus({ jobId: jobIdToStart, silent: false }));
          }
        })
        .catch(error => {
          console.error(`[useDeploy] Initial API call error:`, error);
        });
    } catch (error) {
      console.error(`[useDeploy] Error making initial API call:`, error);
    }
  }, [dispatch, createPollingFunction]);

  // Effect to auto-start polling if deployment is running and this is the active tab
  useEffect(() => {
    if (!jobId) return;

    // Check if this tab is active using the current Redux state
    const state = store.getState();
    const activeTabId = state?.tabs?.activeTab;

    if (!activeTabId) return;

    // Find the active tab
    const activeTab = state?.tabs?.tabs ?
      state.tabs.tabs.find((tab: any) => tab.id === activeTabId) :
      undefined;

    // Only start polling if this jobId belongs to the active tab
    if (activeTab?.state?.jobId !== jobId) return;

    // Auto-start polling if deployment is running
    if (globalDeployStatus === 'running' || globalLatestRunStatus === 'running') {
      //console.log(`[useDeploy] Deployment is running for active tab, ensuring polling is active`);

      // Start polling if not already active
      if (!pollingManager.isPollingActive(jobId)) {
        startPolling(jobId);
      }
    } else {
      // Stop polling if deployment is not running
      if (pollingManager.isPollingActive(jobId)) {
        stopPolling();
      }
    }
  }, [
    jobId,
    globalDeployStatus,
    globalLatestRunStatus,
    startPolling,
    stopPolling
  ]);

  // Clean up polling when component unmounts
  useEffect(() => {
    return () => {
      if (jobId && pollingManager.getActiveJobId() === jobId) {
        stopPolling();
      }
    };
  }, [jobId, stopPolling]);

  // Effect to listen for tab changes in Redux
  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      const state = store.getState();
      const activeTabId = state?.tabs?.activeTab;

      // If the active tab has changed, update the polling manager
      if (activeTabId && pollingManager.getActiveTabId() !== activeTabId) {
        //console.log(`[useDeploy] Active tab changed to ${activeTabId} in Redux`);

        // Update the active tab in the polling manager
        pollingManager.setActiveTabId(activeTabId);

        // Find the active tab
        const activeTab = state?.tabs?.tabs ?
          state.tabs.tabs.find((tab: any) => tab.id === activeTabId) :
          undefined;

        // If the active tab has a jobId and is running, start polling for it
        if (activeTab?.state?.jobId &&
            (activeTab.state.deployStatus === 'running' ||
             activeTab.state.latestRunStatus === 'running')) {
          const activeTabJobId = activeTab.state.jobId;

          // Start polling for this job if it's not already active
          if (!pollingManager.isPollingActive(activeTabJobId)) {
            //console.log(`[useDeploy] Starting polling for newly active tab with jobId ${activeTabJobId}`);
            startPolling(activeTabJobId);
          }
        } else {
          // If the active tab doesn't have a running deployment, stop any polling
          //console.log(`[useDeploy] New active tab has no running deployment, stopping polling`);
          stopPolling();
        }
      }
    });

    // Clean up the subscription when the component unmounts
    return () => {
      unsubscribe();
    };
  }, [startPolling, stopPolling]);

  // Save environment variables
  const saveEnvironmentVariablesLocal = useCallback((envs: EnvValues[]) => {
    if (!jobId) return;

    // Save to Redux state
    dispatch(saveEnvironmentVariables(envs));
    //console.log('Environment variables saved to Redux:', envs);
  }, [jobId, dispatch]);

  // Function to directly update environment variables via API
  const updateEnvironmentVariablesLocal = useCallback(async (envs: EnvValues[]) => {
    if (!jobId) return false;

    try {
      const envsToUpdate = [...envs];
      await agentApi.updateEnvironmentVariables(jobId, envsToUpdate);
      const updatedEnvs = envsToUpdate.map(env => ({
        ...env,
        originalValue: env.value
      }));
      dispatch(saveEnvironmentVariables(updatedEnvs));
      return true;
    } catch (error) {
      console.error("Error updating environment variables:", error);
      return false;
    }
  }, [jobId, dispatch]);

  const loadEnvironmentVariablesLocal = useCallback(async () => {
    if (!jobId) {
      return [];
    }

    try {
      setLocalLoading(true);
      const resultAction = await dispatch(loadEnvironmentVariables(jobId));
      if (loadEnvironmentVariables.fulfilled.match(resultAction)) {
        return resultAction.payload;
      }
      return [];
    } catch (error) {
      console.error("Error loading environment variables:", error);
      return [];
    } finally {
      setLocalLoading(false);
    }
  }, [jobId, dispatch]);

  const loadDeploymentHistoryLocal = useCallback(async () => {
    if (!jobId) {
      return [];
    }

    try {
      setLocalLoading(true);
      const resultAction = await dispatch(loadDeploymentHistory(jobId));
      if (loadDeploymentHistory.fulfilled.match(resultAction)) {
        return resultAction.payload;
      }
      return [];
    } catch (error) {
      console.error("Error loading deployment history:", error);
      return [];
    } finally {
      setLocalLoading(false);
    }
  }, [jobId, dispatch]);

  // Function to check deploy status
  const checkDeployStatusLocal = useCallback((jobId: string, silent: boolean = false) => {
    //console.log(`[useDeploy] Checking deploy status for jobId ${jobId}, silent: ${silent}`);

    // If this is a silent check (from polling), respect the time interval
    if (silent && !shouldMakeApiCall()) {
      //console.log(`[useDeploy] Skipping API call due to time interval restriction`);
      return Promise.resolve();
    }

    // For non-silent checks (user-initiated), always update the timestamp
    if (!silent) {
      lastApiCallRef.current = Date.now();
    }

    // Make sure the current job ID is set in Redux
    dispatch(setCurrentJobIdAction(jobId));

    // Check the deployment status
    return dispatch(checkDeployStatus({ jobId, silent }))
      .then((result) => {
        //console.log(`[useDeploy] Deploy status check result:`, result);

        // Check if the result indicates a running deployment
        if (result.payload && typeof result.payload === 'object' && 'response' in result.payload) {
          const payload = result.payload as any;

          // Check if deployment is running
          const isDeploymentRunning =
            payload.response.status === 'running' ||
            (payload.response.latest_run && payload.response.latest_run.status === 'running');

          // Check if deployment is successful
          const isDeploymentSuccessful =
            payload.response.status === 'success' ||
            (payload.response.latest_run && payload.response.latest_run.status === 'success');

          //console.log(`[useDeploy] Deployment status check: running=${isDeploymentRunning}, success=${isDeploymentSuccessful}`);

          // Check if this is the active tab
          const state = store.getState();
          const activeTabId = state?.tabs?.activeTab;
          const activeTab = state?.tabs?.tabs ?
            state.tabs.tabs.find((tab: any) => tab.id === activeTabId) :
            undefined;
          const isActiveTab = activeTab?.state?.jobId === jobId;

          if (isDeploymentRunning && isActiveTab) {
            //console.log(`[useDeploy] Deployment is running for active tab, starting polling`);
            startPolling(jobId);
          } else if (!isDeploymentRunning && pollingManager.getActiveJobId() === jobId) {
            //console.log(`[useDeploy] Deployment is not running, stopping polling`);
            stopPolling();

            // If the deployment was successful and this is a silent check (from polling),
            // force one more non-silent check to ensure UI updates
            if (isDeploymentSuccessful && silent) {
              //console.log(`[useDeploy] Deployment succeeded, ensuring UI is updated with non-silent check`);
              // Small delay to ensure state has settled
              setTimeout(() => {
                dispatch(checkDeployStatus({ jobId, silent: false }));
              }, 100);
            }
          }
        }

        return result;
      })
      .catch((error) => {
        console.error(`[useDeploy] Deploy status check error:`, error);
        throw error;
      });
  }, [dispatch, startPolling, stopPolling, shouldMakeApiCall]);

  // Handle deployment
  const handleDeploy = useCallback(async (image?: string, deployment_id?: string) => {
    if (jobId) {
      try {
        //console.log(`[useDeploy] Starting deployment for jobId: ${jobId}`);
        setLocalLoading(true);

        dispatch(setCurrentJobIdAction(jobId));

        // Stop any existing polling
        stopPolling();

        // Clear existing deployment steps before starting a new deployment
        // This prevents showing old steps during the transition
        dispatch(resetDeploymentSteps());

        //console.log(`[useDeploy] About to call deployApp API...`);
        // Start the new deployment
        const result = await dispatch(deployApp({ jobId, image, deployment_id }));

        //console.log(`[useDeploy] Deploy result:`, result);
        //console.log(`[useDeploy] Deploy API call completed, result type:`, result.type);

        // Check if this is the active tab
        const state = store.getState();
        const activeTabId = state?.tabs?.activeTab;
        const activeTab = state?.tabs?.tabs ?
          state.tabs.tabs.find((tab: any) => tab.id === activeTabId) :
          undefined;
        const isActiveTab = activeTab?.state?.jobId === jobId;

        if (isActiveTab) {
          //console.log(`[useDeploy] Starting polling for active tab`);
          startPolling(jobId);
        }
      } catch (error) {
        console.error("Error in handleDeploy:", error);
      } finally {
        // Clear loading state after a short delay
        setTimeout(() => {
          setLocalLoading(false);
        }, 300);
      }
    }
  }, [jobId, dispatch, startPolling, stopPolling]);

  const loadDeployLogsLocal = useCallback(async (jobId: string) => {
    if (!jobId) {
      return [];
    }

    try {
      setLocalLoading(true);
      const resultAction = await dispatch(getDeployLogs(jobId));
      if (getDeployLogs.fulfilled.match(resultAction)) {
        return resultAction.payload;
      }
      return [];
    } catch (error) {
      console.error("Error loading deploy logs:", error);
      return [];
    } finally {
      setLocalLoading(false);
    }
  }, [jobId, dispatch]);

  const shutDownDeploymentLocal = useCallback(async () => {
    if (jobId) {
      try {
        //console.log(`[useDeploy] Shutting down deployment for jobId ${jobId}`);
        setLocalLoading(true);

        // Stop any active polling
        if (pollingManager.getActiveJobId() === jobId) {
          stopPolling();
        }

        // Reset the last API call timestamp to ensure we can make a call immediately
        lastApiCallRef.current = 0;

        // Shut down the deployment
        await dispatch(shutDownDeployment(jobId));

        // Force a status check to update the UI
        await checkDeployStatusLocal(jobId, false);
      } catch (error) {
        console.error("Error in shutDownDeployment:", error);
      } finally {
        setTimeout(() => {
          setLocalLoading(false);
        }, 300);
      }
    }
  }, [jobId, dispatch, stopPolling, checkDeployStatusLocal]);

  // Function to set active tab ID
  const setActiveTabIdLocal = useCallback((tabId: string) => {
    //console.log(`[useDeploy] Setting active tab ID to ${tabId}`);

    // Update Redux state
    dispatch(setActiveTabIdAction(tabId));

    // Update the active tab ID in the polling manager
    pollingManager.setActiveTabId(tabId);

    // Get the tab that was activated
    const state = store.getState();
    const activeTab = state?.tabs?.tabs ?
      state.tabs.tabs.find((tab: any) => tab.id === tabId) :
      undefined;

    // If the active tab has a jobId and deployment is running, start polling
    if (activeTab?.state?.jobId &&
        (activeTab.state.deployStatus === 'running' ||
         activeTab.state.latestRunStatus === 'running')) {
      const activeTabJobId = activeTab.state.jobId;
      //console.log(`[useDeploy] Active tab has running deployment, starting polling`);
      startPolling(activeTabJobId);
    } else {
      // Otherwise stop any active polling
      //console.log(`[useDeploy] New active tab has no running deployment, stopping polling`);
      stopPolling();
    }
  }, [dispatch, startPolling, stopPolling]);

  // Function to handle tab closure
  const handleTabClosed = useCallback((tabId: string) => {
    //console.log(`[useDeploy] Handling tab closure for tabId ${tabId}`);

    // Get the state from Redux
    const state = store.getState();

    // Find the tab that was closed
    const closedTab = state?.tabs?.tabs ?
      state.tabs.tabs.find((tab: any) => tab.id === tabId) :
      undefined;

    // If the closed tab had a jobId and we're polling for it, stop polling
    if (closedTab?.state?.jobId && pollingManager.getActiveJobId() === closedTab.state.jobId) {
      //console.log(`[useDeploy] Closed tab was being polled, stopping polling`);
      stopPolling();
    }

    // If this was the current job ID, reset the Redux state
    if (jobId === closedTab?.state?.jobId) {
      //console.log(`[useDeploy] Closed tab was the current job, resetting state`);
      dispatch(resetDeployState());
      dispatch(setCurrentJobIdAction(null));
    }
  }, [dispatch, jobId, stopPolling]);

  // Custom domain methods
  const registerDomain = useCallback(async (domain: string, jobId: string) => {
    if (!domain) return null;

    try {
      setLocalLoading(true);
      const response = await agentApi.registerDomain(domain, jobId);

      // Check if the response has valid data
      if (!response || !response.ip_address) {
        console.error("Invalid domain registration response:", response);
        return {
          status: 'failed',
          message: 'Failed to register domain'
        };
      }

      // Create DNS record from the response
      const dnsRecord = {
        type: 'A',
        name: domain || '', // Use the domain as the host
        value: response.ip_address // Use the IP address from the response
      };

      // Update Redux state with the response
      dispatch(setCustomDomainAction({
        domain: domain,
        status: response.verification_required ? 'pending' : 'verified',
        dns_records: [dnsRecord]
      }));

      return {
        ...response,
        domain,
        status: response.verification_required ? 'pending_verification' : 'connected',
        dns_records: [dnsRecord]
      };
    } catch (error) {
      console.error("Error registering domain:", error);
      return null;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch]);

  const verifyDomain = useCallback(async (domain: string, jobId: string) => {
    if (!domain) return null;

    try {
      setLocalLoading(true);
      const response = await agentApi.verifyDomain(domain, jobId);

      // Check if the response has valid data
      if (!response || !response.status) {
        console.error("Invalid domain verification response:", response);
        return null;
      }

      // Update Redux state with the response
      if (response.status === 'connected' || response.status === 'pending_verification') {
        // Check if we have valid domain and dns_records
        if (response.domain && response.dns_records && response.dns_records.length > 0) {
          // Use the verified boolean from the API response to determine the status
          const domainStatus = response.verified === true ? 'verified' : 'pending';

          dispatch(setCustomDomainAction({
            domain: response.domain,
            status: domainStatus,
            dns_records: response.dns_records
          }));
        } else {
          // If missing required data, set to null
          dispatch(setCustomDomainAction(null));
        }
      }

      return response;
    } catch (error) {
      console.error("Error verifying domain:", error);
      return null;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch]);

  const connectDomain = useCallback(async (domain: string) => {
    if (!domain || !jobId) return null;

    try {
      setLocalLoading(true);
      const response = await agentApi.connectDomain(domain, jobId);

      // Check if the response has valid data
      if (!response || !response.status) {
        console.error("Invalid domain connection response:", response);
        return null;
      }

      // Update Redux state with the response
      if (response.status === 'connected') {
        // Check if we have valid domain and dns_records
        if (response.domain && response.dns_records && response.dns_records.length > 0) {
          dispatch(setCustomDomainAction({
            domain: response.domain,
            status: 'verified',
            dns_records: response.dns_records
          }));
        } else {
          // If missing required data, set to null
          dispatch(setCustomDomainAction(null));
        }
      }

      return response;
    } catch (error) {
      console.error("Error connecting domain:", error);
      return null;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch, jobId]);

  const getDomainStatus = useCallback(async (domain: string) => {
    if (!domain) return null;

    try {
      setLocalLoading(true);
      const response = await agentApi.getDomainStatus(domain);

      // Update Redux state with the response
      if (response) {
        // Check if the response has empty or missing values
        if (!response.domain || response.domain === "" ||
            !response.status ||
            !response.dns_records || response.dns_records.length === 0) {
          // If empty values, set customDomain to null
          dispatch(setCustomDomainAction(null));
        } else {
          dispatch(setCustomDomainAction({
            domain: response.domain,
            status: response.status === 'pending_verification' ? 'pending' :
                    response.status === 'connected' ? 'verified' : 'failed',
            dns_records: response.dns_records || []
          }));
        }
      }

      return response;
    } catch (error) {
      console.error("Error getting domain status:", error);
      return null;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch]);

  const unlinkDomain = useCallback(async (domain: string) => {
    if (!domain || !jobId) return null;

    try {
      setLocalLoading(true);
      const response = await agentApi.unlinkDomain(domain, jobId);

      // Reset the custom domain in Redux state
      dispatch(setCustomDomainAction(null));

      return response;
    } catch (error) {
      console.error("Error unlinking domain:", error);
      return null;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch, jobId]);

  return {
    // Return global state for the current jobId
    deployStatus: globalDeployStatus,
    latestRunStatus: globalLatestRunStatus,
    loading: localLoading,
    deploymentSteps: globalDeploymentSteps,
    deploymentHistory: globalDeploymentHistory,
    currentStepIndex: globalCurrentStepIndex,
    deployUrl: globalDeployUrl,
    customDomainUrl: globalCustomDomainUrl,
    runId: globalRunId,
    errorMessage: globalErrorMessage,
    currentJobId: globalCurrentJobId,
    customDomain: globalCustomDomain,
    deployLogs: globalDeployLogs,

    // Return methods that dispatch Redux actions
    checkDeployStatus: checkDeployStatusLocal,
    loadDeploymentHistory: loadDeploymentHistoryLocal,
    setActiveTabId: setActiveTabIdLocal,
    handleTabClosed,
    loadEnvironmentVariables: loadEnvironmentVariablesLocal,
    saveEnvironmentVariables: saveEnvironmentVariablesLocal,
    updateEnvironmentVariables: updateEnvironmentVariablesLocal,
    envs: globalModifiedEnvs,
    handleDeploy,
    shutDownDeployment: shutDownDeploymentLocal,
    loadDeployLogs: loadDeployLogsLocal,

    // Polling control functions
    startPolling,
    stopPolling,
    isPolling: jobId ? pollingManager.isPollingActive(jobId) : false,

    // Custom domain methods
    registerDomain,
    verifyDomain,
    connectDomain,
    getDomainStatus,
    unlinkDomain
  };
};