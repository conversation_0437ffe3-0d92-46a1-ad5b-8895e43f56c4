import { useState, useEffect } from 'react';

/**
 * Hook that tracks whether the current browser tab is visible or hidden
 * using the Page Visibility API.
 * 
 * @returns {boolean} isVisible - Whether the page is currently visible
 */
export function useDocumentVisibility(): boolean {
  // Initialize with the current visibility state
  const [isVisible, setIsVisible] = useState<boolean>(
    document.visibilityState === 'visible'
  );

  useEffect(() => {
    // Event handler for visibility change
    const handleVisibilityChange = () => {
      setIsVisible(document.visibilityState === 'visible');
    };

    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up event listener on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return isVisible;
}
