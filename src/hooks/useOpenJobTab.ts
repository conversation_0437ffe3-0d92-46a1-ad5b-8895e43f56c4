import { useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { agent<PERSON><PERSON>, JobResponse } from "@/services/agentApi";
import { useTabState } from "@/components/TabBar";

export const useOpenJobTab = () => {
  const { setTabs, setActiveTab, getTabByJobId, updateTabState } = useTabState();
  const navigate = useNavigate();

  const openJobTab = useCallback(async (jobId: string) => {
    try {
      // Check if tab already exists
      const existingTab = getTabByJobId(jobId);
      if (existingTab) {
        setActiveTab(existingTab.id);
        return;
      }

      // Fetch job details
      const response = await agentApi.getJob(jobId);
      const job: JobResponse = response.data;

      if (!job.payload.task) {
        console.error("No task data available");
        return;
      }

      const containerId = job.payload.container_id || job.id;
      if (!containerId) {
        console.error("No container ID available");
        return;
      }

      // Use job ID as tab ID (as per user preference)
      const newTabId = job.id;
      const trajPath = `/root/runs/${job.id}/trajectory.json`;

      const tabState = {
        containerId: job.payload.container_id || job.id,
        initial_commit_id: job.payload.initial_commit_id,
        task: job.payload.task,
        jobId,
        trajPath,
        tabId: newTabId,
        fromJobList: true,
        isCloudFlow: job.payload?.is_cloud,
        clientRefId: job.client_ref_id,
        modelName: job.payload.model_name,
        promptName: job.payload.prompt_name,
        promptVersion: job.payload.prompt_version,
        costLimit: job.payload.per_instance_cost_limit,
        agentName: job.payload.agent_name,
        portMapping: job.payload.portMapping,
        created_by: job.created_by,
      };

      // Create new tab
      setTabs((prevTabs) => [
        ...prevTabs,
        {
          id: newTabId,
          title: `${ job.payload?.original_task || job.payload.task}`,
          path: "/chat",
          state: tabState,
        },
      ]);

      updateTabState(newTabId, tabState);

      // Set active tab and navigate
      setActiveTab(newTabId);

      // Set as active tab

      // Navigate to the job
      navigate(`?job_id=${jobId}`);

    } catch (error) {
      console.error("Error opening job tab:", error);
    }
  }, [setTabs, setActiveTab, getTabByJobId, navigate]);

  return { openJobTab };
};
