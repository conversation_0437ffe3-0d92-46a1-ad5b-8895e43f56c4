import { useState, useCallback } from 'react';
import { agent<PERSON>pi } from '@/services/agentApi';
import { toast } from './use-toast';
import { getRetryAttemptsFromLocalStorage } from '@/lib/utils/rollback';

type RollbackStatus = "INIT" | "IN_PROGRESS" | "COMPLETED" | "ERROR" | "FAILED";

interface UseRollbackProps {
  containerId?: string;
  jobId: string;
  session?: {
    access_token?: string;
  };
  resetPolling: () => void;
  onRollbackSuccess?: () => void; // Callback to clear messages or perform other actions on success
  rollbackRequestId: string | undefined;
  maxAttempts?: number;
}

interface UseRollbackReturn {
  isRollbackLoading: boolean;
  rollbackStatus: RollbackStatus;
  retryAttempted: number;
  handleConfirmRollback: (rollbackType?: 'all' | 'messages') => Promise<void>;
  handleRollbackComplete: (status?: RollbackStatus) => void;
  resetRollbackStatus: () => void;
}

const rollbackApi = async ({ jobId, rollbackRequestId, rollbackType, currentAttempt }: { jobId?: string, rollbackRequestId: string, rollbackType: 'all' | 'messages', currentAttempt: number }) => {
  let response = null;
  const isRetry = currentAttempt > 1;
    if (!jobId) throw new Error('Job ID not available');
    response = await agentApi.rollbackToRequestId(jobId, rollbackRequestId, undefined, rollbackType, isRetry);


  return response;
}


export function useRollback({
  containerId,
  jobId,
  session,
  resetPolling,
  onRollbackSuccess,
  rollbackRequestId,
  maxAttempts = 3,
}: UseRollbackProps): UseRollbackReturn {
  const [isRollbackLoading, setIsRollbackLoading] = useState(false);
  const [rollbackStatus, setRollbackStatus] = useState<RollbackStatus>("INIT");
  const [retryAttempted, setRetryAttempted] = useState(getRetryAttemptsFromLocalStorage(jobId || ''));


  const handleConfirmRollback = useCallback(async (rollbackType: 'all' | 'messages' | undefined = 'all') => {
    
    if (!rollbackRequestId) {
      toast({
        title: "Error",
        description: "No rollback request ID available",
        variant: "destructive",
      });
      return;
    }

    setIsRollbackLoading(true);
    setRollbackStatus("IN_PROGRESS");

    if (retryAttempted >= maxAttempts) {
      setRollbackStatus("FAILED");
    }

    const currentAttempt = retryAttempted + 1;
    try {
      const response = await rollbackApi({ jobId, rollbackRequestId, rollbackType, currentAttempt });
      if (response && response.status !== false) {
        setIsRollbackLoading(false);
        setRollbackStatus("COMPLETED");
        setRetryAttempted(0);
      } else {
        throw new Error("Failed to execute rollback command");
      }
    } catch (error) {
      console.error('Error executing rollback command:', error);
      setIsRollbackLoading(false);
      let title = "Error";
      const currentAttempt = retryAttempted + 1;
      const retryAttemptJobIdMapJson = JSON.parse(localStorage.getItem('rollback_retry_attempt_job_id_map') || '{}');
      localStorage.setItem('rollback_retry_attempt_job_id_map', JSON.stringify({ ...retryAttemptJobIdMapJson, [jobId]: currentAttempt }));

      if (currentAttempt >= maxAttempts) {
        setRollbackStatus("FAILED");
        title = "Failed";
      } else {
        setRollbackStatus("ERROR");
      }

      toast({
        title,
        description: "Failed to execute rollback command",
        variant: "destructive",

      });

    }

  }, [rollbackRequestId, jobId, onRollbackSuccess]);

  const handleRollbackComplete = useCallback((status?: RollbackStatus) => {
    // Close the modal and reset states only if status is provided
    if (status) {
      setRollbackStatus(status);
    }
    resetPolling();
  }, [resetPolling]);

  const resetRollbackStatus = useCallback(() => {
    setRollbackStatus("INIT");
    setIsRollbackLoading(false);
  }, []);

  return {
    isRollbackLoading,
    rollbackStatus,
    retryAttempted,
    handleConfirmRollback,
    handleRollbackComplete,
    resetRollbackStatus
  };
}
