export const getStatusIndicator = (status: string) => {
  switch (status.toLowerCase()) {
    case "running":
      return (
        <span className="flex items-center gap-2 text-[#00B248]">
          <span className="w-2 h-2 rounded-full bg-[#00B248]"></span>Running..
        </span>
      );
    case "waiting":
      return (
        <span className="flex items-center gap-2 text-[#F49B57]">
          <span className="w-2 h-2 rounded-full bg-[#F49B57]"></span>Waiting
        </span>
      );
    case "finished":
      return (
        <span className="flex items-center gap-2 text-[#2EBBE5]">
          <span className="w-2 h-2 rounded-full bg-[#2EBBE5]"></span>Finished
        </span>
      );
    case "paused":
      return (
        <span className="flex items-center gap-2 text-[#8F8F98]">
          <span className="w-2 h-2 rounded-full bg-[#8F8F98]"></span>Paused
        </span>
      );
     case "failed":
      return (
        <span className="flex items-center gap-2 text-[#ED5B5B]">
          <span className="w-2 h-2 rounded-full bg-[#ED5B5B]"></span>Failed
        </span>
      );
    case "resumed":
      return (
        <span className="flex items-center gap-2 text-[#00B248]">
          <span className="w-2 h-2 rounded-full bg-[#00B248]"></span>Running
        </span>
      );
    case "started":
      return (
        <span className="flex items-center gap-2 text-[#2EBBE5]">
          <span className="w-2 h-2 rounded-full bg-[#2EBBE5]"></span>Running
        </span>
      );
    case "env_created":
      return (
         <span className="flex items-center gap-2 text-[#ED5B5B]">
          <span className="w-2 h-2 rounded-full bg-[#ED5B5B]"></span>Failed
        </span>
      );
    default:
      return status;
  }
};


export const generateUniqueTabId = () => {
  return crypto.randomUUID();
};