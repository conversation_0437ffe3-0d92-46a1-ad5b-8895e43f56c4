import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff } from "lucide-react";

// Components
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import EmergentButton from "@/components/EmergentButton";
import RetroGrid from "@/components/RetroGrid";
import Footer from "@/components/Footer";
import Divider from "@/components/DividerWithText";

// Assets
import BorderedLogoDark from "@/assets/logo/borded-logo-dark.svg";
import MailIcon from "@/assets/mail.svg";
import PasswordIcon from "@/assets/password.svg";
import NameIcon from "@/assets/name.svg";
import DiscordIcon from "@/assets/discordLight.svg";
import HypeText from "@/assets/HypeText.svg";

// Hooks
import { useErrorToast } from "@/components/ui/ErrorToast";
import { useAuth } from "@/contexts";
import OAuthButton from "@/components/OAuthButton";
import InputWithIcon from "@/components/ui/inputwithicon";
import { useConfig } from "@/hooks/useConfig";



export default function Authentication({ view }: { view: "login" | "signup" | "activate" }) {
  // Hooks
  const { user, session, loading, signInWithEmail, signUpWithEmail, signInWithOAuth, verifyInviteCode, signOut, refreshSession } = useAuth();
  const navigate = useNavigate();
  const { showErrorToast } = useErrorToast();

  // State
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState<boolean>(false);
  const [isGithubSubmitting, setIsGithubSubmitting] = useState<boolean>(false);
  const [logoutSubmitting, setLogoutSubmitting] = useState<boolean>(false);

  // Form state for each view to prevent sharing values
  const [loginForm, setLoginForm] = useState({ email: "", password: "" });
  const [signupForm, setSignupForm] = useState({ name: "", email: "", password: "" });
  const [activateForm, setActivateForm] = useState({ inviteCode: "" });

  const { config: globalConfig, loading: configLoading } = useConfig();

  // Handle form input changes
  const handleLoginInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSignupInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSignupForm(prev => ({ ...prev, [name]: value }));
  };

  const handleActivateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setActivateForm(prev => ({ ...prev, [name]: value }));
  };

  // Authentication handlers
  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleSubmitting(true);
      const { success, error } = await signInWithOAuth("google");

      if (!success && error) {
        showErrorToast("Error signing in with Google", error);
      }
    } catch (error: any) {
      console.error("Error signing in with Google:", error);
      showErrorToast("Error signing in with Google", error.message || "Failed to sign in");
    } finally {
      setIsGoogleSubmitting(false);
    }
  };

  const handleGithubSignIn = async () => {
    try {
      setIsGithubSubmitting(true);
      const { success, error } = await signInWithOAuth("github");

      if (!success && error) {
        showErrorToast("Error signing in with GitHub", error);
      }
    } catch (error: any) {
      console.error("Error signing in with GitHub:", error);
      showErrorToast("Error signing in with GitHub", error.message || "Failed to sign in");
    } finally {
      setIsGithubSubmitting(false);
    }
  };

  const handleEmailSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { success, error } = await signInWithEmail(loginForm.email, loginForm.password);

      if (!success) {
        if (error?.needsVerification) {
          // Navigation will be handled by the hook
          setIsSubmitting(false);
          return;
        }

        showErrorToast("Error signing in", error || "Invalid email or password");
        setIsSubmitting(false);
        return;
      }

      // If successful, navigation will be handled by useEffect in the hook
    } catch (error: any) {
      console.error("Error signing in:", error);
      showErrorToast("Error signing in", error.message || "Invalid email or password");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInviteCodeSubmit = async (e: React.FormEvent | null, overrideInviteCode?: string) => {
    e?.preventDefault();
    setIsSubmitting(true);
    const inviteCode = overrideInviteCode || activateForm.inviteCode;

    if (!inviteCode.trim()) {
      showErrorToast("Error", "Please enter an invite code");
      setIsSubmitting(false);
      return;
    }

    try {
      const { success, banned, error } = await verifyInviteCode(inviteCode);

      if (!success) {
        console.error("Application error:", error);
        showErrorToast("Error", error || 'Failed to verify invite code');

        if (banned) {
          await signOut();
          window.location.href = '/banned';
          return;
        }

        setIsSubmitting(false);
        return;
      }

      // Refresh session and navigate
      await refreshSession().then(() => {
        navigate("/");
      });
    } catch (error: any) {
      console.error("Error verifying invite code:", error);
      showErrorToast("Error", error.message || "Failed to verify invite code");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEmailSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const { name, email, password } = signupForm;

    if (!name.trim() || !email.trim() || !password.trim()) {
      showErrorToast("Error", "Please fill all the fields");
      setIsSubmitting(false);
      return;
    }

    try {
      const { success, needsVerification, error } = await signUpWithEmail(name, email, password);

      if (!success) {
        showErrorToast("Error signing up", error || "Failed to sign up");
        setIsSubmitting(false);
        return;
      }

      if (needsVerification) {
        //console.log("User signed up, but email not verified");
        // Add a small delay before navigating to prevent the flash redirect
        setTimeout(() => {
          // Use URL parameter instead of state
          navigate(`/verify?email=${encodeURIComponent(email)}`);
        }, 100);
      }
    } catch (error: any) {
      console.error("Error signing up:", error);
      showErrorToast("Error signing up", error.message || "Failed to sign up");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Routing based on auth state
  if (loading) {
    return (
      <div className="flex items-center justify-center w-full h-screen">
        <Spinner className="w-12 h-12" />
      </div>
    );
  }

  // Routing logic
  if (session && user && !user.user_metadata.needs_invite_code) {
    if (view !== "activate") {
      navigate("/");
      return null;
    }
  } else if (session && user && user.user_metadata.needs_invite_code) {
    if (view !== "activate") {
      navigate("/activate");
      return null;
    }
  } else if (!session && view === "activate") {
    navigate("/login");
    return null;
  }

  // View components
  const renderLoginForm = () => (
    <form
      onSubmit={handleEmailSignIn}
      className="p-4 pt-0 md:space-y-[1rem] font-berkeley max-w-[414px] w-full z-[999]"
    >
      <div className="flex flex-col gap-[24px]">

        <div className="flex flex-col items-center justify-center w-full gap-[20px]">
          <OAuthButton
            provider="google"
            onClick={handleGoogleSignIn}
            isSubmitting={isGoogleSubmitting}
            buttonText="Log in with Google"
          />
          <OAuthButton
            provider="github"
            onClick={handleGithubSignIn}
            isSubmitting={isGithubSubmitting}
            buttonText="Log in with Github"
          />
        </div>

        <Divider text="Or Log in with email" />

      </div>

      <div className="flex flex-col w-full space-y-4">
        <div className="mt-2">
          <InputWithIcon
            id="email"
            name="email"
            type="email"
            value={loginForm.email}
            onChange={handleLoginInputChange}
            placeholder="Enter your email"
            required
            disabled={isSubmitting}
            iconSrc={MailIcon}
            iconAlt="Email icon"
          />
        </div>

        <div>
          <div className="relative">
            <InputWithIcon
              id="email"
              name="password"
              type={showPassword ? "text" : "password"}
              value={loginForm.password}
              placeholder="Enter your password"
              onChange={handleLoginInputChange}
              required
              disabled={isSubmitting}
              iconSrc={PasswordIcon}
              iconAlt="Password icon"
            />

            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isSubmitting}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-[#4d4d4d] hover:text-[#CCCCCC] transition-colors w-10 h-10 flex items-center justify-center rounded-md hover:bg-white/5 disabled:opacity-70"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
        </div>
      </div>

      <div className="pt-4">
        <div className='relative'>
          <EmergentButton type="submit" disabled={isSubmitting} onClick={() => { }} variant="light" className="w-full p-0">
            {isSubmitting ? (
              <>
                <span>Logging In</span>
                <Spinner className="ml-2" />
              </>
            ) : (
              <>
                Log In
              </>
            )}
          </EmergentButton>
        </div>
      </div>

      <div className="flex items-center justify-center text-[13px] mt-4 md:text-[16px] w-full gap-2">
        <span onClick={() => navigate("/reset-password")} className="text-[#2EBBE5] underline font-brockmann cursor-pointer">Forgot Password ?</span>
      </div>


    </form>
  );

  const renderSignupForm = () => (
    <form
      onSubmit={handleEmailSignUp}
      className=" p-4 pt-0 space-y-6 max-w-[414px] w-full z-[999] "
    >
      <div className="flex flex-col gap-3 md:gap-[24px]">

        <div className="flex flex-col items-center justify-center w-full gap-3">
          <OAuthButton
            provider="google"
            onClick={handleGoogleSignIn}
            isSubmitting={isGoogleSubmitting}
            buttonText="Sign up with Google"
          />
          <OAuthButton
            provider="github"
            onClick={handleGithubSignIn}
            isSubmitting={isGithubSubmitting}
            buttonText="Sign up with Github"
          />
        </div>
        <Divider text="Or Sign up with email" />
      </div>

      <div className="flex flex-col w-full space-y-2 md:space-y-4">
        <div className="font-berkeley">
          <input
            placeholder="Enter your name"
            id="name"
            name="name"
            type="name"
            value={signupForm.name}
            onChange={handleSignupInputChange}
            required
            disabled={isSubmitting}
            className="w-full h-12 p-4 bg-[#111112] border border-[#333333]
              rounded-md text-white font-inter placeholder-[#4d4d4d] focus:outline-none focus:border-[#336E80]
              transition-colors duration-200 text-[16px] disabled:opacity-70"
          />
        </div>
        <div className="font-berkeley">
          <input
            placeholder="Enter your email"
            id="email"
            name="email"
            type="email"
            value={signupForm.email}
            onChange={handleSignupInputChange}
            required
            disabled={isSubmitting}
            className="w-full h-12 p-4 bg-[#111112] border border-[#333333]
             rounded-md text-white font-inter placeholder-[#4d4d4d] focus:outline-none focus:border-[#336E80]
             transition-colors duration-200 text-[16px] disabled:opacity-70"
          />
        </div>

        <div>
          <div className="relative">
            <input
              id="password"
              name="password"
              value={signupForm.password}
              onChange={handleSignupInputChange}
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              required
              disabled={isSubmitting}
              className="w-full h-12 font-inter p-4 bg-[#111112] border border-[#333333]
               rounded-md text-white placeholder-[#4d4d4d] focus:outline-none focus:border-[#336E80]
               transition-colors duration-200  text-[16px] disabled:opacity-70"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isSubmitting}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-[#4d4d4d] hover:text-[#CCCCCC] transition-colors w-10 h-10 flex items-center justify-center rounded-md hover:bg-white/5 disabled:opacity-70"
            >
              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
        </div>
      </div>

      <div className="flex flex-col md:pt-2">
        <div>
          <div className="relative">
            <EmergentButton type="submit" disabled={isSubmitting} onClick={() => { }} variant="light" className="w-full p-0">
              {isSubmitting ? (
                <>
                  <Spinner className="mr-2" /> Signing Up...
                </>
              ) : (
                "Sign Up"
              )}
            </EmergentButton>
          </div>
        </div>
      </div>

      <div className="flex items-center mt-1">
        <span className="text-[#898e98] text-center text-[13px]">By clicking Sign Up, you agree to our <a href="/terms-of-service" className="text-[#2EBBE5]">Terms of Service</a> and <a href="/privacy-policy" className="text-[#2EBBE5]">Privacy Policy</a>.</span>
      </div>
    </form>
  );

  const renderActivateForm = () => {
    if (!loading) {
      if (!user || !session) {
        // Redirect to login if no session
        navigate("/login");
        return null;
      }

      if (!user.user_metadata.needs_invite_code) {
        // Redirect to home if already activated
        navigate("/");
        return null;
      }
    }

    return (
      <>
        <div className="absolute top-4 right-4 z-[999]">
          <Button
            variant="ghost"
            className="text-white/40 hover:text-white hover:bg-[#313133] max-h-[44px] min-w-[100px] flex items-center justify-center transition-colors"
            onClick={async () => {
              setLogoutSubmitting(true);
              try {
                await signOut();
              } catch (error) {
                console.error("Error signing out:", error);
                showErrorToast("Error signing out", "Failed to sign out");
              } finally {
                setLogoutSubmitting(false);
              }
            }}
            disabled={isSubmitting}
          >
            <div className="flex items-center justify-center w-full ">
              {logoutSubmitting ? <Spinner className="w-4 h-4 mr-2" /> : null}
              Log out
            </div>
          </Button>
        </div>
        <form className="p-4 pt-0 space-y-10 max-w-[414px] w-full z-[999]" onSubmit={(e) => handleInviteCodeSubmit(e)}>
          <div className="flex flex-col w-full space-y-4 z-[999]">
            <div>
              <InputWithIcon
                placeholder="Enter your invite code"
                id="inviteCode"
                name="inviteCode"
                type="text"
                value={activateForm.inviteCode}
                onChange={handleActivateInputChange}
                required
                disabled={isSubmitting}
                className="w-full h-12 p-4 bg-[#111112] border border-[#333333]
                     rounded-md text-white placeholder-[#4d4d4d] focus:outline-none focus:border-[#336E80]
                     transition-colors duration-200 text-[16px] disabled:opacity-70 font-inter"
              />
            </div>
          </div>
          <div className="flex flex-col w-full space-y-8 z-[999]">
            <div className='relative'>
              <EmergentButton type="submit" disabled={isSubmitting} onClick={() => { }} variant="light" className="w-full p-0">
                {isSubmitting ? (
                  <>
                    <span>Activating</span>
                    <Spinner className="ml-2" />
                  </>
                ) : (
                  <>
                    Let's Go
                  </>
                )}
              </EmergentButton>
            </div>


            <Divider text="Don't have an invite code yet?" />
            {!(globalConfig?.lite_version || {})?.enabled && !configLoading && <div className='relative'>
              <EmergentButton type="submit" disabled={isSubmitting} onClick={() => {
                window.open("https://discord.gg/X9cqNxYyG8");
              }} variant="light" className="w-full p-0 bg-[#6673FF] bg-gradient-to-b from-[#6673FF] to-[#5865F2] text-white">
                <img src={DiscordIcon} alt="" className="w-6 h-6" />
                Join Discord
              </EmergentButton>

              <img src={HypeText} alt="" className="hidden md:block  md:absolute -bottom-[150%] -right-[20%] h-[85px]" />

            </div>}
            {
              (globalConfig?.lite_version || {})?.enabled && !configLoading && <EmergentButton type="button" disabled={isSubmitting} onClick={() => {
                handleInviteCodeSubmit(
                  null,
                  globalConfig?.lite_version.code
                )

              }} variant="light" className="w-full p-0 bg-[#6673FF] bg-gradient-to-b from-[#6673FF] to-[#5865F2] text-white">
                {isSubmitting ? (
                  <>
                    <span>Join Lite Version</span>
                    <Spinner className="ml-2" />
                  </>
                ) : (
                  <>
                    Join Lite Version
                  </>
                )}
              </EmergentButton>
            }
          </div>
        </form>
      </>
    );
  };

  return (
    <>
      <div className="relative flex flex-col items-center justify-center w-full h-screen overflow-y-scroll p-4 Z-[999]">
        <div className="flex flex-col items-center mb-[3rem] z-[999]">
          <img onClick={() => navigate("/")} // Add this line to make the logo clickable
            src={BorderedLogoDark}
            className="w-16 h-16 mb-2 cursor-pointer md:mb-8 md:w-20 md:h-20"
            alt="Emergent Logo"
          />
          <div className="font-sans font-medium text-center">
            <span className="text-[28px] md:text-[40px] font-brockmann text-white">
              {
                view === "login" ? "Welcome to Emergent" : view === "signup" ? "Create an Account" : "Activate your Account"
              }
            </span>
            <span className="block text-[#898e98] text-[13px]  md:text-base mt-1 max-w-[414px] w-full">
              {
                view === "login" ? <>
                  <span>
                    Don't have an account?{" "}
                  </span>
                  <button
                    onClick={() => navigate("/register")}
                    className="text-[#2EBBE5] font-brockmann text-[13px]  md:text-base hover:text-[#2EBBE5] transition-colors duration-200"
                  >
                    Sign up for free
                  </button>
                </> : view === "signup" ? <>
                  <span>
                    Already have an account?{" "}
                  </span>
                  <button
                    onClick={() => navigate("/login")}
                    className="text-[#2EBBE5] font-brockmann text-[13px]  md:text-base hover:text-[#2EBBE5] transition-colors duration-200"
                  >
                    Log In
                  </button>
                </> : <>
                  Welcome to Emergent! We're currently in private beta. To get started, Please enter your invitation code.
                </>
              }
            </span>
          </div>
        </div>

        {
          view === "login" ? (
            renderLoginForm()
          ) : view === "signup" ? (
            renderSignupForm()
          ) : (
            renderActivateForm()
          )
        }
        <Footer className="bottom-0 z-[998] pointer-events-none" />
        <RetroGrid
          height="50%"
          top="50%"
          gridSizeX={70}
          gridSizeY={30}
          fadeIntensity={90}
          gridLineWidth={0.3}
          gridOpacity={0.6}
          backgroundColor="#131314"
        />
      </div>
    </>
  );
}