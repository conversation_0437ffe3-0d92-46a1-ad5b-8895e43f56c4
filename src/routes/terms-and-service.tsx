import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import Footer from "@/components/Footer";
import RetroGrid from "@/components/RetroGrid";
import BorderedLogoDark from "@/assets/logo/borded-logo-dark.svg";
import StyledMarkdown from "@/components/StyledMarkdown";

export default function TermsAndService() {
  return (
    <div className="flex flex-col min-h-screen bg-[#0f0f10] text-white relative">
      <div className="sticky top-0 z-20 justify-between flex items-center pl-6 md:px-[4rem] gap-4 h-[80px] bg-[#0f0f10]">
        <div className="flex items-center gap-4">
          <Link to="/">
            <img
              src={BorderedLogoDark}
              alt="Logo"
              className="w-[48px] h-[48px]"
            />
          </Link>
          <div className="flex flex-col items-start">
            <h1 className="text-xl font-bold md:text-2xl">Terms of Service</h1>
            <p className="text-[#898e98] text-sm">Last updated: 20 May 2025</p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Link to="/">
            <Button variant="outline" className="border-[#3D3D3D] text-white">
              Return to Home
            </Button>
          </Link>
        </div>
      </div>
      <div className="z-10 flex flex-col items-center w-full p-4 overflow-y-auto md:p-8">
        <div className="max-w-6xl w-full bg-[#1A1A1C] px-6 overflow-y-scroll h-[75vh] rounded-lg shadow-lg mb-8">
          <StyledMarkdown variant="default">
            {`# Complete Updated Emergent Labs Inc. Terms of Service
## 1. Introduction
### 1.1 Agreement Overview
These Terms of Service ("Terms") constitute a legal agreement between you and Emergent Labs Inc. ("we," "our," or "us"). Your use of emergent.sh (the "site") and related services (collectively, the "Services") is subject to these Terms. By downloading, installing, accessing, or using our Services, you:
Acknowledge that you have read and understood these Terms
Agree to be bound by these Terms
Agree to our Privacy Policy (available at emergent.sh/privacy)
Commit to comply with all applicable laws and regulations If you do not agree with these Terms, you must not download, install, access, or use our Services.
### 1.2 Definitions
"Site" refers to our website and associated tools for developing software.
"Content" means code, projects, data, files, and other materials that you create, modify, or access through our Services.
"User" means any individual who downloads, installs, accesses, or uses our Services.
"Authorized User" means a User who is authorized by you to access and use the Services under your account.
"Input" means the prompts, commands, and instructions you provide to the Services.
"Output" means the code, responses, and other content generated by the Services based on your Input.
## 2. License to Use Our Services
### 2.1 Rights Granted
Subject to your compliance with these Terms, Emergent grants you a limited, personal, non-exclusive, non-transferable license to download, install, and use our Services for their intended purpose. Depending on your subscription plan:
Free Plan: Limited to personal, non-commercial use
Paid Plans: May be used for commercial purposes according to the terms of your subscription
### 2.2 Restrictions
You may not, and you may not permit others to:
Distribute, sell, rent, lease, or sublicense our Services
Remove or alter any proprietary notices or labels on our Services
Use our Services in a way that could harm, disable, overburden, or impair our systems
Use automated scripts to collect information from or interact with our Services
Use our Services for any illegal purpose or in violation of any applicable laws
### 2.3 Reservation of Rights
All rights not expressly granted to you in these Terms are reserved by Emergent. You acknowledge that you do not acquire any ownership rights in the Services by downloading, installing, or using them.
## 3. User Accounts and Content
### 3.1 Account Creation and Management
You may be required to create an account to use certain features of our Services
You must provide accurate, current, and complete information when creating your account
You are responsible for maintaining the confidentiality of your account credentials
You are responsible for all activities that occur under your account
You must notify us immediately of any unauthorized use of your account
### 3.2 Minimum Age
You must be an adult to use the Service. If you are younger than 13 we will allow you to use the Service if you first provide us with your parent or guardian’s written consent. If you are a parent or legal guardian of a user under the age of 18, by allowing your child to use the Service, you are subject to these Terms and responsible for your child’s activity on the Service.
### 3.3 Account Deletion Process
You may request deletion of your account <NAME_EMAIL>. Upon account deletion:
Your user profile will be removed
Your access to the Services will be terminated
Content stored on our servers will be deleted within 90 days
We may retain certain information as required by law or for legitimate business purposes
We will handle any remaining content according to our Privacy Policy
### 3.4 User Content
By using our Services, you may create, modify, or upload Content. You retain all ownership rights to your Content, subject to the licenses granted herein.
### 3.5 Licenses to User Content
You grant Emergent a non-exclusive, worldwide, royalty-free license to access, use, process, copy, distribute, perform, export, and display your Content, only as reasonably necessary:
To provide and maintain the Services
To address service or technical issues
To comply with legal obligations
As otherwise permitted by these Terms This license does not grant us the right to sell your Content or otherwise distribute it outside our Services.
### 3.6 Content Backups
You are responsible for backing up your Content. While we implement reasonable measures to protect your Content, we do not guarantee that your Content will not be lost or corrupted.
## 4. Services and Pricing
### 4.1 Plans and Limits
We offer various subscription plans that provide different features, usage limits, and capabilities. Current plan details and pricing are available at emergent.sh/pricing. Each plan has specific usage limitations, including but not limited to:
Number of projects
Compute resources
Storage capacity
Database connections
Integration capabilities We reserve the right to modify our plans and pricing at any time.
### 4.2 Subscription and Payment Terms
Our service is subscription-based with recurring billing (monthly or annual)
Subscriptions automatically renew unless canceled before the renewal date
Payments are non-refundable unless otherwise specified
You authorize us to charge your payment method for all fees due
We may suspend or terminate your access if payment fails
### 4.3 Support Services
Technical support availability depends on your subscription plan:
Free Plan: Limited community support
Paid Plans: Priority email support
Enterprise Plans: Dedicated support and service level agreements Support is limited to platform-related issues and does not include application debugging or development consulting.
### 4.4 Detailed Service Delivery Information
Access to our platform is granted immediately upon successful payment processing
You will receive login credentials via email
If you experience any issues with access, contact <EMAIL>
We strive to maintain continuous availability of our Services, but we do not guarantee uninterrupted access. Scheduled maintenance, updates, or unforeseen technical issues may result in temporary service interruptions. We will make reasonable efforts to notify users of planned maintenance when possible.
### 4.5 Refund Policies
All payments are non-refundable unless otherwise determined by us
In cases where refunds are approved, they will be processed to the original payment method
Refunds may be considered in cases of:
Billing errors
Duplicate charges
Other circumstances at our sole discretion
### 4.6 Cancellation Policy
You can cancel your subscription at any time by:
Clicking on the Credits button
Clicking Manage your Subscriptions button
Clicking on Edit billing
Clicking on Cancel Subscription button Cancellation Terms:
Your subscription remains active until the end of the current billing period
You will continue to have full access until the end of your paid period
Your subscription will not automatically renew after cancellation
Any unused credits expire at the end of the subscription
No partial refunds are provided for unused portions of the billing period
## 5. Acceptable Use
### 5.1 Prohibited Conduct
While using our Services, you agree not to:
Violate any applicable laws or regulations
Infringe the intellectual property rights of others
Transmit harmful code or attempt to compromise our systems
Interfere with other users' use of the Services
Conduct unauthorized penetration testing or security assessments
Circumvent or attempt to defeat usage limits or monitoring
Misuse computational resources or APIs
Attempt to gain unauthorized access to any part of our Services
### 5.2 Content Restrictions
You agree not to use our Services to create, upload, or process Content that:
Is unlawful, harmful, threatening, abusive, or harassing
Is defamatory, vulgar, obscene, or otherwise objectionable
Contains malware, viruses, or malicious code
Infringes any patent, trademark, copyright, or other proprietary rights
Contains unsolicited or unauthorized advertising
Promotes illegal activities
Violates third-party terms of service
### 5.3 Resource Usage
We may establish quotas and limits on your usage of our Services, including computational resources, storage, and API calls. You agree to comply with these limits, which may vary based on your subscription plan. Excessive or abusive usage that negatively impacts our Services or other users may result in throttling, suspension, or termination of your access.
### 5.4 Export Controls and Sanctions Compliance
Emergent's Services may never be used to develop anything subjected to sanctions or other export restrictions, or in any way that is not compliant with the laws of the jurisdiction that Emergent or the user operates in. The Services may also never be used in regions, or by anyone subject to sanctions or export restrictions.
Users are responsible for ensuring their use of our Services complies with all applicable export control laws and regulations, including those of the United States and other jurisdictions. By using our Services, you represent and warrant that you are not located in any country or territory subject to U.S. or other applicable trade sanctions or embargoes, and that you are not on any denied persons list or similar restricted party list.
## 6. Data Usage, Training, and Learning
### 6.1 Service Improvement and Training
We collect and analyze usage data to improve our Services. This includes:
Performance metrics and error patterns
Feature usage statistics and patterns
User interface interactions
Command execution patterns We may use this data to enhance our AI systems, improve code generation accuracy, and optimize platform performance.
### 6.2 AI Features and Training
Our Services include AI-powered features that analyze your code and generate suggestions. By default:
We do not use your proprietary code to train our general AI models without consent
We process your code and commands within our secure environment
You can control AI assistance features through application settings
Enterprise plan users may have additional controls and restrictions regarding AI training.
### 6.3 Training Data Biases
Emergent acknowledges that AI systems may exhibit biases inherent in their training data. While we strive to minimize such biases, we cannot guarantee that our Services are entirely free from them. Emergent specifically disclaims liability for:
Training data biases affecting code generation
Unintended bias in generated content
AI outputs that reflect historical, social, or cultural biases
Unintended consequences of using AI-generated code that may contain inherent biases
Users should review and test all generated code to ensure it meets their requirements and values.
### 6.4 Enterprise Considerations
For Enterprise users, we provide enhanced data protection:
We explicitly waive our right to use their data for training purposes
Custom data handling agreements are available
No sharing with third parties occurs without explicit consent
Data residency options may be available upon request
Enhanced security measures are implemented
Dedicated support for data protection concerns is provided
Enterprise customers seeking additional information about these protections <NAME_EMAIL>.
### 6.5 Data Retention and Management
For Enterprise users, we provide enhanced data protection:
Options to opt out of certain data collection
Custom data handling agreements available
Strict controls on third-party data sharing
Data residency options where available
## 7. Intellectual Property Rights
### 7.1 Emergent Ownership
Emergent owns and retains all rights, title, and interest in and to: Brand Elements:
The "Emergent" name and brand
Emergent logos, designs, and visual elements
The "emergent.sh" domain and related domains Platform Elements:
Application architecture and design
All platform features and functionalities
Our proprietary algorithms and systems
User interface and experience design
Documentation and supporting materials These elements are protected by copyright, trademark, trade dress, patent laws, and other intellectual property rights.
### 7.2 User Rights
To be explicitly clear, while Emergent owns all rights to the platform and brand elements described above, this ownership is entirely separate from and does not extend to:
Code generated using our Services
Applications built with our tools
Custom configurations and implementations
Modified or derivative works created from generated code
Your own code commits and changes You retain all ownership rights to code created using our Services, subject to any third-party open source licenses that may apply to components used.
### 7.3 Usage Rights
You may freely:
Use generated code commercially
Modify and adapt generated code
Distribute generated code in any form
Sell applications built using our platform
Open source your implementations Emergent places no restrictions on your use of generated code and makes no claim to ownership of applications you develop.
## 8. Security and Privacy
### 8.1 Data Security
We implement reasonable security measures to protect your Content and account information. These measures include:
Encryption of sensitive data
Secure development practices
Access controls and authentication
Regular security assessments However, no method of transmission or storage is 100% secure. You acknowledge and accept this risk when using our Services.
### 8.2 Personal Data
Our collection, use, and protection of personal data is governed by our Privacy Policy, available at emergent.sh/privacy. By using our Services, you consent to the data practices described in our Privacy Policy. If you use our Services to process personal data of others, you must:
Obtain necessary consents for processing such data
Process such data in accordance with applicable data protection laws
Not use our Services to process sensitive personal data without appropriate safeguards
### 8.3 Data Processing Agreement
If you are subject to data protection regulations such as GDPR and process personal data through our Services, you may request our Data Processing Agreement <NAME_EMAIL>.
## 9. Term and Termination
### 9.1 Term
These Terms will commence when you download, install, or first use our Services and will continue until terminated as described below.
### 9.2 Termination by You
You may terminate these Terms at any time by:
Canceling your subscription
Deleting your account (<NAME_EMAIL>)
### 9.3 Termination by Emergent
We may suspend or terminate your access to the Services at any time if:
You violate these Terms
You fail to pay any fees when due
We are required to do so by law
We decide to discontinue the Services We will make reasonable efforts to notify you of any suspension or termination.
### 9.4 Effect of Termination
Upon termination:
Your license to use our Services will end
You must cease all use of our Services
You will lose access to your account and Content stored on our servers
Emergent will delete your Content within 90 days after termination (unless legally required to retain it)
You will not receive a refund for any prepaid fees
### 9.5 Survival
The following sections will survive termination: Intellectual Property Rights, Disclaimer of Warranties, Limitation of Liability, Indemnification, and any other provisions that by their nature should survive termination.
## 10. Disclaimer of Warranties
YOUR USE OF THE SERVICES IS ENTIRELY AT YOUR OWN RISK. THE SERVICES ARE PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT ANY GUARANTEES OR WARRANTIES OF ANY KIND. TO THE FULLEST EXTENT PERMITTED BY LAW, EMERGENT AND OUR SUPPLIERS AND LICENSORS EXPLICITLY DISCLAIM ALL WARRANTIES, WHETHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE, AND NON-INFRINGEMENT.
WE MAKE NO GUARANTEES REGARDING THE ACCURACY, RELIABILITY, OR USEFULNESS OF THE SERVICES OR ANY CONTENT GENERATED THROUGH THE SERVICES, AND YOUR USE OF THESE IS ENTIRELY AT YOUR OWN RISK.
SPECIFICALLY, WE DO NOT WARRANT THAT:
THE SERVICES WILL MEET YOUR REQUIREMENTS
THE SERVICES WILL BE UNINTERRUPTED, TIMELY, SECURE, OR ERROR-FREE
THE RESULTS FROM THE USE OF THE SERVICES WILL BE ACCURATE OR RELIABLE
THE QUALITY OF THE SERVICES WILL MEET YOUR EXPECTATIONS
ANY ERRORS IN THE SERVICES WILL BE CORRECTED
EMERGENT SPECIFICALLY DISCLAIMS ALL LIABILITY FOR:
AI-GENERATED CODE THAT CONTAINS BUGS, ERRORS, OR SECURITY VULNERABILITIES
CODE THAT FAILS TO MEET YOUR SPECIFIC REQUIREMENTS OR EXPECTATIONS
GENERATED CODE THAT BECOMES OBSOLETE OR INCOMPATIBLE WITH OTHER SYSTEMS
ANY ISSUES RESULTING FROM INTEGRATION WITH OTHER SOFTWARE OR SERVICES
ANY FORM OF DATA LOSS OR CORRUPTION
SOME JURISDICTIONS DO NOT ALLOW THE EXCLUSION OF IMPLIED WARRANTIES, SO SOME OR ALL OF THE ABOVE EXCLUSIONS MAY NOT APPLY TO YOU.
## 11. Limitation of Liability
TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, EMERGENT, ITS AFFILIATES, OFFICERS, EMPLOYEES, AGENTS, SUPPLIERS AND LICENSORS WILL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR EXEMPLARY DAMAGES, INCLUDING BUT NOT LIMITED TO DAMAGES FOR LOSS OF PROFITS, GOODWILL, USE, DATA, OR OTHER INTANGIBLE LOSSES (EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGES), RESULTING FROM:
YOUR USE OF OR INABILITY TO USE THE SERVICES
ANY CHANGES TO THE SERVICES OR TERMINATION OF THE SERVICES
UNAUTHORIZED ACCESS TO OR ALTERATION OF YOUR CONTENT
STATEMENTS OR CONDUCT OF ANY THIRD PARTY ON THE SERVICES
ANY OTHER MATTER RELATING TO THE SERVICES
IN NO EVENT SHALL OUR TOTAL LIABILITY TO YOU FOR ALL CLAIMS ARISING FROM OR RELATED TO THE SERVICES EXCEED THE AMOUNT PAID BY YOU TO EMERGENT DURING THE TWELVE (12) MONTHS IMMEDIATELY PRECEDING THE EVENT GIVING RISE TO THE CLAIM.
### 11.1 AI-Generated Code Liability Details
Emergent Labs Inc. accepts no liability or responsibility for:
AI-generated code that:
Contains bugs, errors, or security vulnerabilities
Fails to meet your specific requirements or expectations
Does not achieve your intended business or technical goals
Becomes obsolete or incompatible with other systems
Causes issues when integrated with other software
Results in any form of data loss or corruption
AI system limitations or failures, including:
Incorrect or incomplete code generation
Misunderstanding of your requirements or prompts
Generation of non-optimal or inefficient solutions
Inconsistencies in code output quality
Failures to follow best practices or coding standards
### 11.2 Business Impact Liability
Emergent Labs Inc. accepts no liability or responsibility for business impact resulting from:
Reliance on generated code in production systems
Time or resources spent modifying generated code
Project delays or missed deadlines
Additional development costs or technical debt
Integration challenges with existing systems
Customer or user dissatisfaction
Furthermore, we disclaim liability for third-party related issues:
Compatibility problems with external services
Licensing issues in generated code
Security vulnerabilities in recommended dependencies
Changes in external APIs or services
Conflicts with other development tools
THIS LIST IS NOT EXHAUSTIVE, AND EMERGENT'S LIMITATION OF LIABILITY EXTENDS TO ALL POSSIBLE ISSUES, WHETHER LISTED HERE OR NOT, ARISING FROM THE USE OF OUR AI-POWERED PLATFORM AND SERVICES.
THESE LIMITATIONS WILL APPLY EVEN IF THE ABOVE STATED REMEDY FAILS OF ITS ESSENTIAL PURPOSE.
## 12. Indemnification
You agree to defend, indemnify, and hold harmless Emergent, its affiliates, officers, directors, employees, and agents from and against any claims, liabilities, damages, losses, costs, expenses, or fees (including reasonable attorneys' fees) arising from:
Your use of the Services
Your Content
Your violation of these Terms
Your violation of any rights of another person or entity
Applications, websites, or services you create using our Services
## 13. Miscellaneous
### 13.1 Governing Law
These Terms shall be governed by and construed in accordance with the laws of the State of California, without regard to its conflict of law principles.
### 13.2 Dispute Resolution
Any dispute arising from or relating to these Terms or your use of the Services shall be resolved exclusively in the state or federal courts located in Alameda County, California, and you consent to the personal jurisdiction of such courts.
### 13.3 Entire Agreement
These Terms, together with our Privacy Policy and any other legal notices or additional terms and conditions provided by Emergent, constitute the entire agreement between you and Emergent relating to your use of the Services.
### 13.4 Severability
If any provision of these Terms is found to be invalid or unenforceable, the remaining provisions will remain in full force and effect.
### 13.5 Waiver
Our failure to enforce any right or provision of these Terms will not be considered a waiver of such right or provision.
### 13.6 Assignment
You may not assign or transfer these Terms or your rights under these Terms without our prior written consent. Emergent may assign these Terms without restriction.
### 13.7 Modifications to Terms
We may modify these Terms at any time by posting the revised Terms on our website and/or within the desktop application. Your continued use of the Services after any changes will constitute your acceptance of such changes. For significant changes, we will make reasonable efforts to notify you through the Services or via email. If you do not agree to the updated Terms, you must stop using the Services.
### 13.8 Force Majeure
We will not be liable for any failure or delay in performance resulting from causes beyond our reasonable control, including but not limited to acts of God, natural disasters, pandemic, war, terrorism, riots, civil unrest, government actions, labor disputes, Internet service provider failures or any other unforeseen events.
## 14. Contact Information
If you have any questions about these Terms, please contact us at: Email: <EMAIL> Address: 2380 Via Espada, Pleasanton, CA 94566, USA
## 15. Promotions and User Feedback
### 15.1 Promotions
We may offer promotions, contests, or sweepstakes, which may have additional terms and conditions.
### 15.2 User Feedback
We welcome feedback and suggestions
We may implement any feedback without compensation
Providing feedback grants us a perpetual, worldwide license to use it
No credit or compensation is due for implemented suggestions
By submitting feedback, you grant Emergent a royalty-free, worldwide, transferable, sub-licensable, irrevocable, perpetual license to use, implement, modify, and distribute your feedback in any way and for any purpose.
## 16. DMCA Compliance
We respect intellectual property rights and comply with the Digital Millennium Copyright Act (DMCA). For any DMCA-related issues, contact <NAME_EMAIL> with:
Identification of the copyrighted work claimed to be infringed
Identification of the allegedly infringing material
Your contact information
A statement of good faith belief in the infringement
A statement of accuracy under penalty of perjury
Upon receipt of a valid DMCA notice, we will take appropriate action, which may include removing or disabling access to the allegedly infringing material.
## 17. Abuse Reporting
If you encounter abuse of our Services, violations of these Terms, or other concerning behavior, please report <NAME_EMAIL>. Reports should include:
Description of the abusive or concerning behavior
Relevant dates and times
Any supporting information or evidence
Your contact information (optional)
We take abuse reports seriously and will investigate promptly, taking appropriate action as necessary.`}
          </StyledMarkdown>
        </div>
      </div>

      <div className="mt-auto">
        <Footer className="z-[998] relative" />
      </div>
      <div className="fixed inset-0 z-0">
        <RetroGrid
          height="50%"
          top="50%"
          gridSizeX={70}
          gridSizeY={30}
          fadeIntensity={90}
          gridLineWidth={0.3}
          gridOpacity={0.6}
          backgroundColor="#131314"
        />
      </div>
    </div>
  );
}
