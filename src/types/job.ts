export interface Job {
    id: string;
    status: string;
    created_at: string;
    created_by: string;
    client_ref_id?: string;
    payload: {
      title?: string;
      description?: string;
      task?: string;
      original_task?: string;
      project?: string;
      container_id?: string;
      traj_path?: string;
      initial_commit_id?: string;
      is_cloud?: boolean;
      model_name?: string;
      prompt_name?: string;
      prompt_version?: string;
      per_instance_cost_limit?: number;
      agent_name?: string;
      portMapping?: { service: string; hostPort: number; containerPort: number }[];
      base64_image_list?: any;
      forked?: {
        parent_job_id: string | null;
        parent_job_title: string | null;
      };
      is_public?: boolean;

    };
    model_name?: string;
  }