import { ResponseImageData } from './message';

export interface TrajectoryItem {
  step_num: number;
  thought: string;
  action: string | null;
  observation: string | null;
  function_name: string | null;
  env_success?: boolean;
  agent_name: string | null;
  timestamp: string;
  human_message: string | null | undefined;
  acc_cost?: number;
  max_budget?: number;
  max_bugdet?: number;
  cmd_execution_mode?: string;
  expertise_type?: string;
  error?: boolean;
  error_message?: string;
  request_id?: string;
  base64_image_list?: ResponseImageData[];
  enable_rollback?: boolean;
  next_call_agent?: string | null;
  warning_for_token_limit?: boolean;
  current_token_count?: number;
  max_token_count?: number;
  traj_payload?: any;
  error_ts?: string;
  switching_to_build_mode?: boolean;
  forked_job_id?: string;
  fork_status?: "running" | "success" | "failed" | null;
}
